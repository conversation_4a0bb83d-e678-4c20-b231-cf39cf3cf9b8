import { TexShapePos } from "../../scripts/model/config/TexFillConfig";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Editor/EditorFillItem")
export default class EditorFillItem extends cc.Component {

    private _num: number = 0;
    @property()
    get num() {
        return this._num;
    }
    set num(v) {
        if (!v || v == this._num) return;
        this._num = v;
        this.resetPos();
    }

    private _col: number = 1;
    @property()
    get col() {
        return this._col;
    }
    set col(v) {
        if (!v || v == this._col || v > 9) return;
        this._col = v;
        this.resetColor();
    }

    public init(col: number, list: TexShapePos[]) {
        this.col = col;
        this.node.name = "color_" + this.col;
        this._num = list.length;
        for (let i = 0; i < list.length; i++) {
            let n = this.node.children[i];
            if (!n) {
                n = cc.instantiate(this.node.children[0]);
                n.setParent(this.node);
            }
            n.active = true;
            n.setPosition(cc.v2(list[i].x, list[i].y));
        }
        for (let i = this._num; i < this.node.childrenCount; i++) {
            this.node.children[i].active = false;
        }
    }


    protected resetInEditor() {
        this.resetPos();
    }

    private resetPos() {
        for (let i = 0; i < this._num; i++) {
            let n = this.node.children[i];
            if (!n) {
                n = cc.instantiate(this.node.children[0]);
                n.setParent(this.node);
            }
            n.active = true;
        }
        for (let i = this._num; i < this.node.childrenCount; i++) {
            this.node.children[i].active = false;
        }
        this.resetColor();
    }

    private resetColor() {
        this.node.name = "color_" + this.col;
        let col = this.getColor();
        this.node.children.forEach((c) => {
            c.color = col;
        });
    }

    getColor() {
        switch (this.col) {
            case 1:
                return cc.color(218, 187, 142, 255);
            case 2:
                return cc.color(201, 39, 36, 255);
            case 3:
                return cc.color(201, 88, 25, 255);
            case 4:
                return cc.color(255, 214, 1, 255);
            case 5:
                return cc.color(123, 247, 39, 255);
            case 6:
                return cc.color(25, 247, 249, 255);
            case 7:
                return cc.color(34, 185, 255, 255);
            case 8:
                return cc.color(244, 107, 255, 255);
            case 9:
                return cc.color(109, 67, 52, 255);
            default:
                return cc.color();
        }
    }

}
