import { TexFillConfigItem, TexShapeConfigItem, TexShapePos } from "../../scripts/model/config/TexFillConfig";
import EditorFillItem from "./EditorFillItem";
import EditorTextureFill from "./EditorTextureFill";

const { ccclass, property, menu } = cc._decorator;

const EXPORT_LEVEL = 4;
const EXPORT_PATH = "/Users/<USER>/Documents/Flat/game_c_colorsort/assets/bundles/config/texFill/"

@ccclass
@menu("Editor/EditorTexMgr")
export default class EditorTexMgr extends cc.Component {

    private _level: number = 1;
    @property()
    get level() {
        return this._level;
    }
    set level(v) {
        if (!v) return;
        this._level = v;
        this.loadingLevelInEditor();
    }

    start() {
        let editorTexFill = this.node.getChildByName("EditorTextureFill").getComponent(EditorTextureFill);
        let config = new TexFillConfigItem();
        config.shapes = [];
        config.level = EXPORT_LEVEL;
        for (let i = 0; i < editorTexFill.shape.children.length; i++) {
            let shape = editorTexFill.shape.children[i];
            let shapeItem = new TexShapeConfigItem();
            shapeItem.posList = [];
            let col = 1;
            if (shape.name.includes("color_")) {
                col = Number(shape.name.split("_")[1]);
            }
            shapeItem.color = col;
            shape.children.forEach(cc => {
                if (!cc.active) return;
                let pos = new TexShapePos();
                pos.x = this.toFixed3(cc.x + shape.x);
                pos.y = this.toFixed3(cc.y + shape.y);
                shapeItem.posList.push(pos);
                cc.active = false;
            });
            config.shapes.push(shapeItem);
        }

        editorTexFill.Init(config);
        this.node.getChildByName("exportBtn").on("click", () => {
            let content = JSON.stringify(config);
            let suc = jsb.fileUtils.writeStringToFile(content, EXPORT_PATH + EXPORT_LEVEL + ".json");
            console.log(suc ? "export success" : "export fail");
        }, this);
    }

    toFixed3(num: number): number {
        return Math.round(num * 1000) / 1000
    }

    private async loadingLevelInEditor() {
        let editorFillNode = this.node.getChildByName("EditorTextureFill");
        let shapeName = this.level + "_" + "shape";
        let originName = this.level + "_" + "origin";
        let startName = this.level + "_" + "start";
        let originTex = await this.loadEditorRes<cc.Texture2D>("bundles/game/texture/fill/" + originName + ".png");

        let shapeSpf = await this.loadEditorRes<cc.SpriteFrame>("bundles/game/texture/fill/" + shapeName + ".png/" + shapeName);
        let originSpf = await this.loadEditorRes<cc.SpriteFrame>("bundles/game/texture/fill/" + originName + ".png/" + originName);
        let startSpf = await this.loadEditorRes<cc.SpriteFrame>("bundles/game/texture/fill/" + startName + ".png/" + startName);
        let originNode = editorFillNode.getChildByName("origin");
        originNode.setContentSize(originTex.width, originTex.height);

        let startNode = editorFillNode.getChildByName("start");
        startNode.setContentSize(originTex.width, originTex.height);

        let shapeNode = editorFillNode.getChildByName("shape");
        shapeNode.setContentSize(originTex.width, originTex.height);

        originNode.getComponent(cc.Sprite).spriteFrame = originSpf;
        startNode.getComponent(cc.Sprite).spriteFrame = startSpf;
        shapeNode.getComponent(cc.Sprite).spriteFrame = shapeSpf;

        let displayNode = editorFillNode.getChildByName("display");
        displayNode.setContentSize(originTex.width, originTex.height);

        shapeNode.removeAllChildren();

        let configItem: TexFillConfigItem = (await this.loadEditorRes<cc.JsonAsset>("bundles/config/texFill/" + this.level + ".json"))?.json as TexFillConfigItem;
        if (!configItem) {
            return;
        }
        var shapePosPrfab = editorFillNode.getComponent(EditorTextureFill).shapePosPrefab;
        configItem.shapes.forEach(it => {
            let n = cc.instantiate(shapePosPrfab);
            n.setParent(shapeNode);
            n.setPosition(cc.v3());
            n.getComponent(EditorFillItem).init(it.color, it.posList);
        });
    }


    async loadEditorRes<T extends cc.Asset>(path: string): Promise<T> {
        return new Promise<T>((resolve) => {
            var uuid = Editor.assetdb.remote.urlToUuid("db://assets/" + path);
            if (uuid) {
                cc.assetManager.loadAny(uuid, function (err, asset: T) {
                    if (err) {
                        Editor.error("load err: " + path);
                        resolve(null);
                    } else {
                        resolve(asset);
                    }

                });
            } else {
                Editor.error(path + " not found");
                resolve(null);
            }
        });
    }

}
