import RenderUtil from "../../scripts/colorfill/RenderUtil";
import { TexFillConfigItem } from "../../scripts/model/config/TexFillConfig";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Editor/EditorTextureFill")
export default class EditorTextureFill extends cc.Component {
    @property(cc.Node) origin: cc.Node = null;
    @property(cc.Node) shape: cc.Node = null;
    @property(cc.Node) target: cc.Node = null;
    @property(cc.Sprite) display: cc.Sprite = null;
    @property(cc.Prefab) shapePosPrefab: cc.Prefab = null;

    private originData: Uint8Array;
    private shapeData: Uint8Array;
    private displayData: Uint8Array;

    private texture: cc.Texture2D;
    private spriteFrame: cc.SpriteFrame;

    public Init(config: TexFillConfigItem) {
        this.origin.active = true;
        this.shape.active = true;
        this.target.active = true;
        this.display.node.active = true;

        this.originData = RenderUtil.getPixelsData(this.origin);
        this.shapeData = RenderUtil.getPixelsData(this.shape);
        this.displayData = RenderUtil.getPixelsData(this.target);

        this.texture = new cc.Texture2D();
        this.spriteFrame = new cc.SpriteFrame();
        this.renderTexture();


        for (let i = 0; i < config.shapes.length; i++) {
            let posList = config.shapes[i].posList;
            setTimeout(() => {
                for (let j = 0; j < posList.length; j++) {
                    this.fillTextureColor(posList[j].x, posList[j].y);
                }
                this.renderTexture();
            }, (i + 1) * 500);
        }
    }

    private fillTextureColor(x: number, y: number): void {
        x = this.translateX(this.shape.width, x);
        y = this.translateY(this.shape.height, y);
        this.fillColor(x, y);
    }

    private isCanSet(x: number, y: number): boolean {
        let index = this.positionToBufferIndex(this.target.width, x, y);
        return this.isIdxCanSet(index);
    }

    private translateX(width: number, x: number): number {
        return Math.trunc(x + width * 0.5);
    }

    private translateY(heigth: number, y: number): number {
        return Math.trunc(-y + heigth * 0.5);
    }

    private positionToBufferIndex(width: number, x: number, y: number, colorSize = 4): number {
        return Math.trunc(x + y * width) * colorSize;
    }

    private isIdxCanSet(idx: number): boolean {
        if (idx < 0 || idx + 3 > this.shapeData.length || this.shapeData[idx + 3] > 1) return false;

        for (let i = idx; i < idx + 4; i++) {
            if (this.displayData[i] != this.originData[i])
                return true;
        }
        return false;
    }


    private setColor(x: number, y: number): boolean {
        let index = this.positionToBufferIndex(this.target.width, x, y);
        if (index < 0 || index + 3 > this.shapeData.length) return false;
        for (let i = index; i < index + 4; i++) {
            this.displayData[i] = this.originData[i];
        }
        return true;
    }

    private fillColor(x: number, y: number) {
        let colorPointList1 = [{ x, y }];
        let colorPointList2 = [];
        while (colorPointList1.length > 0) {
            for (let item of colorPointList1) {
                this.setColor(item.x, item.y);
                for (let offset of [cc.v2(0, +1), cc.v2(0, -1), cc.v2(-1, 0), cc.v2(+1, 0)]) {
                    let nx = item.x + offset.x, ny = item.y + offset.y;
                    if (!this.isCanSet(nx, ny)) {
                        continue;
                    }
                    if (colorPointList2.find(v => v.x == nx && v.y == ny) == null) {
                        colorPointList2.push({ x: nx, y: ny });
                    }
                }
            }
            colorPointList1 = colorPointList2;
            colorPointList2 = [];
        }
        // this.renderTexture();
    }

    private renderTexture() {
        this.texture.initWithData(this.displayData, cc.Texture2D.PixelFormat.RGBA8888, this.origin.width, this.origin.height);
        this.spriteFrame.setTexture(this.texture);
        this.display.spriteFrame = this.spriteFrame;
    }

    protected onDestroy(): void {
        this.originData = null;
        this.displayData = null;
        this.originData = null;
    }

}
