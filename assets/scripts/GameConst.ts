import { ILanguage } from "./frame/localization/LocalizationMgr";
import { UIConf } from "./frame/ui/UIManager";

export const ShapeType = cc.Enum({
    Strip: 1,
    Circle: 2,
});

export enum DeviceType {
    Default,
    Low,
    Middle,
    High
}

export enum UIID_MAIN {
    null,
    UIGuide,
    WinPop,
    LosePop,
    RevivePop,
}

export let CONFIGS = {
    "GuideConfig": "GuideConfig",
    "LevelConfig": "LevelConfig"
}

export let UICF_MAIN: { [key: number]: UIConf } = {
    [UIID_MAIN.UIGuide]: { prefab: "prefab/GuidePage", bundle: "common" },
    [UIID_MAIN.WinPop]: { prefab: "prefab/WinPop", bundle: "pop" },
    [UIID_MAIN.LosePop]: { prefab: "prefab/LosePop", bundle: "pop" },
    [UIID_MAIN.RevivePop]: { prefab: "prefab/RevivePop", bundle: "pop" },
}

export let LANGUAGE_CONFIG: ILanguage = {
    support: ["en", "in", "id", "pt", "th", "vi", "es", "ms", "ko", "ja", "fr", "de"],
    default: "en",
    configUrl: "LanguageConfig"
};


export default class GameConst {

}

//广告位ID
export class ADPlacementName {

}
