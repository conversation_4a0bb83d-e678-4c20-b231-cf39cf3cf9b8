import { LocalizationMgr } from "./LocalizationMgr";
import Component = cc.Component;
import Label = cc.Label;
import _decorator = cc._decorator;
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass()
@menu('LocalizedLabel')
@executeInEditMode
export class LocalizedLabel extends Component {
    label: Label | null = null;

    @property({ visible: false })
    key: string = '';

    @property({ displayName: 'Key', visible: true })
    get _key() {
        return this.key;
    }
    set _key(str: string) {
        this.updateLabel();
        this.key = str;
    }

    onLoad() {
        this.fetchRender();
    }

    fetchRender() {
        let label = this.getComponent('cc.Label') as Label;
        if (label) {
            this.label = label;
            this.updateLabel();
            return;
        }
    }

    updateLabel() {
        if (this.label && LocalizationMgr.ins) {
            let str = LocalizationMgr.ins.t(this.key);
            if (str)
                this.label.string = str;
        }
    }
}
