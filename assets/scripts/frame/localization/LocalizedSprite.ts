import { LocalizationMgr } from "./LocalizationMgr";
import _decorator = cc._decorator;
import Sprite = cc.Sprite;
import SpriteFrame = cc.SpriteFrame;
import Component = cc.Component;
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass('LocalizedSpriteItem')
class LocalizedSpriteItem {
    @property()
    language: string = 'zh';
    @property({
        type: SpriteFrame,
    })
    spriteFrame: SpriteFrame | null = null;
}

@ccclass()
@menu('LocalizedSprite')
@executeInEditMode
export class LocalizedSprite extends Component {
    sprite: Sprite | null = null;

    @property({
        type: LocalizedSpriteItem,
    })
    spriteList = [];

    onLoad() {
        this.fetchRender();
    }

    fetchRender() {
        let sprite = this.getComponent('cc.Sprite') as Sprite;
        if (sprite) {
            this.sprite = sprite;
            this.updateSprite();
            return;
        }
    }

    updateSprite() {
        for (let i = 0; i < this.spriteList.length; i++) {
            const item = this.spriteList[i];
            if (item.language === (LocalizationMgr.ins && LocalizationMgr.ins.language)) {
                if (this.sprite == null) {
                    let sprite = this.getComponent('cc.Sprite') as Sprite;
                    this.sprite = sprite;
                }
                this.sprite && (this.sprite.spriteFrame = item.spriteFrame);
                break;
            }
        }
    }
}
