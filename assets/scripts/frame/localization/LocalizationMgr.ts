import { LoaderTools } from "../utils/LoaderTools";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { LogUtil } from "../utils/LogUtil";
import StringUtil from "../utils/StringUtil";
import { LocalizedLabel } from "./LocalizedLabel";
import { LocalizedSprite } from "./LocalizedSprite";

/**
 * 解决小语种换行问题
 */

cc.game.once(cc.game.EVENT_ENGINE_INITED, () => {
    // @ts-ignore
    cc.textUtils.label_wordRex = /([a-zA-Z0-9ÄÖÜäöüßéèçàùêâîôûа-яА-ЯЁё\u0900-\u097F\uA8E0-\uA8FF]+|\S)/;
    // @ts-ignore
    cc.textUtils.label_symbolRex = /^[!,.:;'}]%?>、‘“》？。，！]/;
    // @ts-ignore
    cc.textUtils.label_lastWordRex = /([a-zA-Z0-9ÄÖÜäöüßéèçàùêâîôûаíìÍÌïÁÀáàÉÈÒÓòóŐőÙÚŰúűñÑæÆœŒÃÂãÔõěščřžýáíéóúůťďňĚŠČŘŽÁÍÉÓÚŤżźśóńłęćąŻŹŚÓŃŁĘĆĄ-яА-ЯЁё\u0900-\u097F\uA8E0-\uA8FF]+|\S)$/;
    // @ts-ignore
    cc.textUtils.label_lastEnglish = /[a-zA-Z0-9ÄÖÜäöüßéèçàùêâîôûаíìÍÌïÁÀáàÉÈÒÓòóŐőÙÚŰúűñÑæÆœŒÃÂãÔõěščřžýáíéóúůťďňĚŠČŘŽÁÍÉÓÚŤżźśóńłęćąŻŹŚÓŃŁĘĆĄ-яА-ЯЁё\u0900-\u097F\uA8E0-\uA8FF]+$/;
    // @ts-ignore
    cc.textUtils.label_firstEnglish = /^[a-zA-Z0-9ÄÖÜäöüßéèçàùêâîôûаíìÍÌïÁÀáàÉÈÒÓòóŐőÙÚŰúűñÑæÆœŒÃÂãÔõěščřžýáíéóúůťďňĚŠČŘŽÁÍÉÓÚŤżźśóńłęćąŻŹŚÓŃŁĘĆĄ-яА-ЯЁё\u0900-\u097F\uA8E0-\uA8FF]/;
});

export interface ILanguage {
    /**支持列表**/
    support: string[];
    /**支持列表以外默认语言**/
    default: string;
    /**支持列表**/
    configUrl: string;
}

export class LocalizationMgr {
    private static _ins: LocalizationMgr;
    public static get ins() {
        return this._ins = this._ins || new LocalizationMgr();
    }
    public static ilanguage: ILanguage;
    private languageConfig: cc.JsonAsset = null;//语言表配置
    private languageKeyMap = {};//语言表可读取配置

    private keyMap = { pt: "Portuguese", in: "Hindi", id: "Indonesian", en: "English", zh: "English", th: "Thai", vi: "Vietnamese", es: "Spanish", ms: "Malay", ko: "Korean", ja: "Japanese", fr: "French", de: "German" };
    private sdkLanMap = { hi: 'in', in: 'id', en: 'en', pt: 'pt', th: 'th', vi: 'vi', es: "es", ms: "ms", ko: "ko", ja: "ja", fr: "fr", de: "de" };

    readonly SAVE_KEY = "custom_language_new";

    private _curLanguage: string = "en";
    constructor() {
        this._curLanguage = LocalStorageMgr.getStringValue(this.SAVE_KEY, "");
        if (this._curLanguage == "") {
            this._curLanguage = "en";
        }
    }

    public loadLanguageConfig(url: string): Promise<boolean> {
        return new Promise<boolean>(async (resolve) => {
            let config = await LoaderTools.loadRes<cc.JsonAsset>('config', url);
            if (!config) {
                resolve(false);
            } else {
                this.languageConfig = config;
                this.initLanguage();
                resolve(true);
            }
        })
    }

    t(key: string, ...params) {
        const searcher = [key];
        let data = null;
        for (let i = 0; i < searcher.length; i++) {
            if (this.languageKeyMap[searcher[i]] && this.languageKeyMap[searcher[i]][this.keyMap[this._curLanguage]]) {
                data = this.languageKeyMap[searcher[i]][this.keyMap[this._curLanguage]];
            }
            if (data == null) {
                return '';
            }
        }
        data = StringUtil.replaceLanStr(data);
        if (params) {
            return StringUtil.substitute(data || "", ...params)
        } else {
            return data || '';
        }
    }

    updateSceneRenderers() { // very costly iterations
        const rootNodes = cc.director.getScene()!.children;
        const allLocalizedLabels: any[] = [];
        for (let i = 0; i < rootNodes.length; ++i) {
            let labels = rootNodes[i].getComponentsInChildren(LocalizedLabel);
            Array.prototype.push.apply(allLocalizedLabels, labels);
        }
        for (let i = 0; i < allLocalizedLabels.length; ++i) {
            let label = allLocalizedLabels[i];
            if (!label.node.active) continue;
            label.updateLabel();
        }
        const allLocalizedSprites: any[] = [];
        for (let i = 0; i < rootNodes.length; ++i) {
            let sprites = rootNodes[i].getComponentsInChildren(LocalizedSprite);
            Array.prototype.push.apply(allLocalizedSprites, sprites);
        }
        for (let i = 0; i < allLocalizedSprites.length; ++i) {
            let sprite = allLocalizedSprites[i];
            if (!sprite.node.active) continue;
            sprite.updateSprite();
        }
    }

    get language() {
        return this._curLanguage;
    }

    changeCurrentLanguage(lang: string) {
        LogUtil.print("changeCurrentLanguage ", lang)
        this._curLanguage = lang;
        LocalStorageMgr.setStringValue(this.SAVE_KEY, lang);

        this.updateSceneRenderers();

    }

    initSdkLang(sdk_lan: string) {
        if (LocalStorageMgr.getStringValue(this.SAVE_KEY, "") != "") {
            return;
        }

        const sdk_lan_new = sdk_lan.split('_')[0];
        LogUtil.print("initSdkLang ", sdk_lan, sdk_lan_new);

        if (this.sdkLanMap[sdk_lan_new]) {
            this.changeCurrentLanguage(this.sdkLanMap[sdk_lan_new]);
        } else {
            this.changeCurrentLanguage(LocalizationMgr.ilanguage.default);
        }

    }

    public initLanguage() {
        this.languageKeyMap = {};
        this.languageConfig.json.forEach(lan => {
            this.languageKeyMap[lan.LanguageKey] = lan;
        });
    }
}
