import { AesManager } from "../http/AesManager";
import { ApiConfig } from "../http/ApiConfig";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { LogUtil } from "../utils/LogUtil";
import App from "./App";

export class LogMgr {
    static readonly LOGS = "logs";
    static readonly AD_LOGS = "ad_logs";

    public static checkLog() {
        if (cc.sys.isBrowser) {
            return
        }
        let LogList = LocalStorageMgr.getObject(this.LOGS);
        if (!LogList.log) {
            LogList.log = [];
        }
        let adList = LocalStorageMgr.getObject(this.AD_LOGS);
        if (!adList.log) {
            adList.log = [];
        }
        if (LogList.log.length >= 1) {
            let sendLog = "";
            let sendLogList = [];
            for (let i = 0; i < Math.min(20, LogList.log.length); i++) {
                sendLog = sendLog + "|#|" + LogList.log[i];
                sendLogList.push(LogList.log[i]);
            }
            let commonData = JSON.parse(App.platform.getCommonParams()) || {};
            let sendData = { anm: "", did: "", subanm: "", debugging: "", uid: "", log_data: "", com_param: "" };
            sendData.anm = App.info.anm;
            sendData.subanm = App.info.subanm;
            sendData.uid = App.device.aid;
            sendData.did = commonData.gaid || "";
            sendData.debugging = commonData.debugging || "";
            sendData.log_data = AesManager.encryptLog(sendLog);
            App.doPostLog(this.addLogApi, sendData, (data: any) => {
                LogUtil.print(this.addLogApi + " Sucess=>", JSON.stringify(data));
                if (data.status == 1) {
                    let LogList = LocalStorageMgr.getObject(this.LOGS);
                    if (!LogList.log) {
                        LogList.log = [];
                    }
                    sendLogList.forEach(log => {
                        if (LogList.log.indexOf(log) > -1)
                            LogList.log.splice(LogList.log.indexOf(log), 1);
                    })
                    LocalStorageMgr.setObject(this.LOGS, LogList);
                }
            }, (error: any) => {
                LogUtil.print(this.addLogApi + " Error=>", JSON.stringify(error));
            });
        }
        if (adList.log.length >= 1) {
            let sendLog = "";
            let sendLogList = [];
            for (let i = 0; i < Math.min(50, adList.log.length); i++) {
                sendLog = sendLog + "|#|" + adList.log[i];
                sendLogList.push(adList.log[i]);
            }
            let commonData = JSON.parse(App.platform.getCommonParams()) || {};
            let sendData = { anm: "", did: "", subanm: "", debugging: "", uid: "", log_data: "", com_param: "" };
            sendData.anm = App.info.anm;
            sendData.subanm = App.info.subanm;

            sendData.did = commonData.gaid || "";
            sendData.debugging = commonData.debugging || "";
            sendData.uid = App.device.aid;
            sendData.log_data = AesManager.encryptLog(sendLog);

            App.doPostLog(this.addLogApi, sendData, (data: any) => {
                LogUtil.print(this.addLogApi + " Sucess=>", JSON.stringify(data));
                if (data.status == 1) {
                    let LogList = LocalStorageMgr.getObject(this.AD_LOGS);
                    if (!LogList.log) {
                        LogList.log = [];
                    }
                    sendLogList.forEach(log => {
                        if (LogList.log.indexOf(log) > -1)
                            LogList.log.splice(LogList.log.indexOf(log), 1);
                    })
                    LocalStorageMgr.setObject(this.AD_LOGS, LogList);
                }
            }, (error: any) => {
                LogUtil.print(this.addLogApi + " Error=>", JSON.stringify(error));
            });

        }
    }

    public static addLog(name, data: object) {
        if (cc.sys.isBrowser) {
            return
        }
        let commonData = JSON.parse(App.platform.getCommonParams()) || {};
        commonData.anm = App.info.anm;
        commonData.sub = "";
        data = Object.assign(data, commonData);
        let dataStr = `action=${name}`;
        data["anm"] = App.info.anm;
        data["subanm"] = App.info.subanm;
        for (let key in data) {
            dataStr = dataStr + "`" + `${key}=${data[key]}`;
        }
        let sendData = { anm: "", did: "", subanm: "", debugging: "", uid: "", log_data: "", com_param: "" };
        sendData.anm = App.info.anm;
        sendData.subanm = App.info.subanm;
        sendData.uid = App.device.aid;
        sendData.did = commonData.gaid || "";
        sendData.debugging = commonData.debugging || "";
        sendData.log_data = AesManager.encryptLog(dataStr);
        let LogList = LocalStorageMgr.getObject(this.LOGS);
        let adList = LocalStorageMgr.getObject(this.AD_LOGS);
        if (!LogList.log) {
            LogList.log = [];
        }
        if (!adList.log) {
            adList.log = [];
        }
        if (name.includes("ad_")) {
            adList.log.push(dataStr);
        } else {
            LogList.log.push(dataStr);
        }
        LogList.log.forEach(eventStr => {
            let arr = eventStr.split("`");
            arr.forEach(
                arrStr => {
                    if (arrStr.includes("logtime")) {
                        let time = Number(arrStr.split("=")[1]);
                        if ((Date.now() - time) > 72 * 60 * 60 * 1000) {
                            if (LogList.log.indexOf(eventStr) > -1)
                                LogList.log.splice(LogList.log.indexOf(eventStr), 1);
                        }
                    }
                }
            )
        })
        if (LogList.log.length > 100) {
            LogList.log.shift();
        }
        adList.log.forEach(eventStr => {
            let arr = eventStr.split("`");
            arr.forEach(
                arrStr => {
                    if (arrStr.includes("logtime")) {
                        let time = Number(arrStr.split("=")[1]);
                        if ((Date.now() - time) > 72 * 60 * 60 * 1000) {
                            if (adList.log.indexOf(eventStr) > -1)
                                adList.log.splice(adList.log.indexOf(eventStr), 1);
                        }
                    }
                }
            )
        })
        if (adList.log.length > 50) {
            adList.log.shift();
        }
        LocalStorageMgr.setObject(this.LOGS, LogList);
        LocalStorageMgr.setObject(this.AD_LOGS, adList);
        LogUtil.print("addLogPrint", dataStr);
        App.doPostLog(this.addLogApi, sendData, (data: any) => {
            LogUtil.print(this.addLogApi + " Sucess=>", JSON.stringify(data));
            if (data.status == 1) {
                let LogList = LocalStorageMgr.getObject((name.includes("ad_") ? this.AD_LOGS : this.LOGS));
                if (!LogList.log) {
                    LogList.log = [];
                }
                LogList.log.splice(LogList.log.indexOf(dataStr), 1);
                LocalStorageMgr.setObject((name.includes("ad_") ? this.AD_LOGS : this.LOGS), LogList);
            }
        }, (error: any) => {
            LogUtil.print(this.addLogApi + " Error=>", JSON.stringify(error));
        });
    }

    static get addLogApi() {
        return ApiConfig.logDomain + "/api/log/addlogs";
    }
}