import { IPlatform } from "../platform/IPlatform";
import Platform_Android from "../platform/Platform_Android";
import Platform_Web from "../platform/Platform_Web";
import { EventManager } from "../event/EventManager";
import { uiManager } from "../ui/UIManager";
import { LoaderTools } from "../utils/LoaderTools";
import { ILanguage, LocalizationMgr } from "../localization/LocalizationMgr";
import HttpMgr from "../http/HttpMgr";
import { AppInfo } from "./AppInfo";
import { LogUtil } from "../utils/LogUtil";
import DeviceInfo from "../model/DeviceInfo";
import Utils from "../utils/Utils";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { UIID } from "../const/UIID";
import { ApiConfig } from "../http/ApiConfig";


/**全局门面类**/
export default class App {

    public static info: AppInfo;        // app信息
    public static platform: IPlatform;  // platform 
    public static device: DeviceInfo;   // 设备信息

    public static _appNode: cc.Node;
    public static readonly appNodeName: string = "AppRoot";

    public static initPlatform(info: AppInfo) {
        window['App'] = App;
        this.info = info;

        let platform = App.getPlatform();

        App.platform = platform;
        this.device = platform.getDeviceInfo();

        ApiConfig.isDebug = this.device.IsTestPack();
        LogUtil.logging = this.device.IsTestPack();
    }

    /**
     * 初始化游戏
     * @param language 多语言设置
     * @param complete 初始化完成回调
     */
    public static async initialize(language?: ILanguage): Promise<boolean> {

        return new Promise<boolean>(async (resolve, reject) => {
            let launchPrefab = await LoaderTools.loadRes<cc.Prefab>("common", 'prefab/AppRoot');
            App._appNode = cc.instantiate(launchPrefab);
            App._appNode.setPosition(-9999, -9999, -9999);
            cc.game.addPersistRootNode(App._appNode);
            App.platform.startUp();

            await this.initLan(language);

            return resolve(true);
        })
    }


    public static doGet(url: string, data?: any, complete?: Function, error?: Function, headers?: { name: string, value: string }): void {
        var token = this.device.gameToken;
        if (headers == null && token && token != "") {
            headers = { name: "token", value: token };
        }
        this.http.doGet(url, data, complete, error, headers);
    }

    public static doPost(url: string, data?: any, complete?: Function, error?: Function, headers?: { name: string, value: string },): void {
        var token = this.device.gameToken;
        if (headers == null && token && token != "") {
            headers = { name: "token", value: token };
        }
        this.http.doPost(url, data, headers, complete, error);
    }

    public static doPostLog(url: string, data?: any, complete?: Function, error?: Function): void {
        let headers = { name: "Content-Type", value: "application/x-www-form-urlencoded" }
        this.http.doPostLog(url, data, headers, complete, error);
    }

    public static async doPostAsync<T>(url: string, reqData: any, visible: boolean = true, retry: boolean = false, isRetry: boolean = false): Promise<T> {
        if (visible)
            uiManager.open(UIID.UILoadComponent);
        LogUtil.print(url + " doPostAsync=>", JSON.stringify(reqData));
        return new Promise<T>(resolve => {
            App.doPost(url, reqData, (data: any) => {
                LogUtil.print(url + " Sucess=>", JSON.stringify(data));
                if (data.code == 0 || data.code == 200) {
                    if (isRetry) {
                        App.platform.loadAd(true);
                    }
                    resolve(data.data || {});
                    if (visible)
                        uiManager.closeById(UIID.UILoadComponent);
                }
                else {
                    let tipMsg: string = LocalizationMgr.ins.t(data.message);
                    if (tipMsg == "") tipMsg = data.message;
                    uiManager.tip(tipMsg);
                    if (tipMsg?.includes('limit has been used up')) {
                        resolve({ isException: true } as any);
                    } else {
                        resolve(null);
                    }
                    LogUtil.print(url + " Error=> code:", data.code);
                    if (visible)
                        uiManager.closeById(UIID.UILoadComponent);
                }
            }, (error: any) => {
                LogUtil.print(url + " Error=>", error);
                if (visible)
                    uiManager.closeById(UIID.UILoadComponent);
                if (retry) {
                    uiManager.open(UIID.UINetError, async () => {
                        let res = await this.doPostAsync<T>(url, reqData, visible, retry, true);
                        resolve(res);
                    });
                } else {
                    uiManager.tip(LocalizationMgr.ins.t("ToastServerFailed"));
                    resolve(null);
                }

            }, null);
        });
    }


    public static async doGetAsync<T>(url: string, reqData: any, visible: boolean = true, retry: boolean = false, isRetry: boolean = false, showToast: boolean = true): Promise<T> {
        if (visible)
            uiManager.open(UIID.UILoadComponent);
        const showLoadPm = Utils.sleep(0.6);
        LogUtil.print(url + " doGetAsync=>", JSON.stringify(reqData));
        return new Promise<T>(resolve => {
            App.doGet(url, reqData, (data: any) => {
                LogUtil.print(url + " Sucess=>", JSON.stringify(data));
                if (data.code == 0 || data.code == 200) {
                    if (isRetry) {
                        App.platform.loadAd(true);
                    }
                    resolve(data.data || {});
                    if (visible)
                        uiManager.closeById(UIID.UILoadComponent);
                }
                else {
                    resolve(null);
                    if (LocalizationMgr.ins.t(data.message) == "") {
                        if (showToast) uiManager.tip(data.message);
                    } else {
                        if (showToast) uiManager.tip(LocalizationMgr.ins.t(data.message));
                    }
                    LogUtil.print(url + " Error=> code:", data.code);
                    if (visible)
                        uiManager.closeById(UIID.UILoadComponent);
                }
            }, async (error: any) => {
                LogUtil.print(url + " Error=>", error);
                if (visible) {
                    await showLoadPm;
                    uiManager.closeById(UIID.UILoadComponent);
                }
                if (visible)
                    uiManager.closeById(UIID.UILoadComponent);
                if (retry) {
                    uiManager.open(UIID.UINetError, async () => {
                        let res = await this.doGetAsync<T>(url, reqData, visible, retry, true);
                        resolve(res);
                    });
                } else {
                    if (showToast) uiManager.tip(LocalizationMgr.ins.t("ToastServerFailed"));
                    resolve(null);
                }
            }, null);
        })
    }

    public static async checkVersion(): Promise<boolean> {
        return new Promise<boolean>(async (resolve, reject) => {
            resolve(true);
        });
    }

    public static initializeView(uiConf: any, uiConfMain: any): void {
        let CF = {}
        if (uiConfMain) {
            CF = Object.assign(uiConf, uiConfMain);
        }
        uiManager.initUIConf(CF);
    }

    public static async initLan(language: ILanguage) {
        LocalizationMgr.ilanguage = language;
        await LocalizationMgr.ins.loadLanguageConfig(language.configUrl);
        if (this.device.language != "") {
            LocalizationMgr.ins.initSdkLang(this.device.language);
        }
    }


    private static getPlatform(): IPlatform {
        let platform: IPlatform;

        if (cc.sys.platform == cc.sys.ANDROID) {
            platform = new Platform_Android();
        } else {
            platform = new Platform_Web();
        }
        return platform;
    }

    get fcmToken() {
        return LocalStorageMgr.getStringValue("fcm_token", "");
    }

    set fcmToken(v) {
        LocalStorageMgr.setStringValue("fcm_token", v);
    }

    // public static async initConfigs(configList) {
    //     let configs = {};
    //     for (const key in configList) {
    //         let url = configList[key];
    //         let config = await LoaderTools.loadRes<cc.JsonAsset>('Config', url);
    //         if (config) {
    //             configs[key] = config.json;
    //         }
    //     }
    //     ConfigManager.ins.initAllConfig(configs);
    // }

    public static get event(): EventManager { return EventManager.getInstance() }
    public static get http(): HttpMgr { return HttpMgr.ins }
}
