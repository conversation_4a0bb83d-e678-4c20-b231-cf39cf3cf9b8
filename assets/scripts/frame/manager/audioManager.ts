const clamp01 = cc.misc.clamp01;
import { LoaderTools } from "../utils/LoaderTools";
import App from "../app/App";
const { ccclass, property } = cc._decorator;

@ccclass
export class audioManager extends cc.Component {

    @property(cc.AudioSource) audioSource: cc.AudioSource = null;

    private static _instance: audioManager;
    static get instance() {
        if (this._instance) {
            return this._instance;
        }
    }

    protected onLoad(): void {
        audioManager._instance = this;
    }

    private soundDir = 'sound/'

    soundVolume: number = 1;
    musicVolume: number = 1;
    vibrateOpen: number = 1;

    setting(sound, music, vibrate) {
        if (sound) {
            this.openSound();
        } else {
            this.closeSound();
        }
        if (music) {
            this.openMusic();
        } else {
            this.closeMusic();
        }
        this.vibrateOpen = vibrate;
    }
    /**
     * 播放音乐
     * @param {Boolean} loop 是否循环播放
     */
    playMusic(name: string, bundle = "sound") {
        LoaderTools.loadRes<cc.AudioClip>(bundle, name).then((clip) => {
            cc.audioEngine.setMusicVolume(this.musicVolume);
            cc.audioEngine.playMusic(clip as cc.AudioClip, true);
        });
    }

    stopMusic() {
        cc.audioEngine.stopMusic();
    }

    /* 播放音效
    * @param {String} name 音效名称可通过constants.AUDIO_SOUND 获取
    */
    playSound(name: string, bundle = "sound") {
        if (this.soundVolume == 0) return;
        LoaderTools.loadRes<cc.AudioClip>(bundle, name).then((clip) => {
            cc.audioEngine.setEffectsVolume(this.soundVolume);
            cc.audioEngine.playEffect(clip as cc.AudioClip, false);
        });
    }

    countdownClipAudioId: number;
    playCountdownClip() {
        if (this.soundVolume == 0) return;
        LoaderTools.loadRes<cc.AudioClip>("sound", "count_down").then((clip) => {
            cc.audioEngine.setEffectsVolume(this.soundVolume);
            this.countdownClipAudioId = cc.audioEngine.playEffect(clip as cc.AudioClip, false);
        });
    }

    stopCountdownClip() {
        if (this.countdownClipAudioId) {
            cc.audioEngine.stopEffect(this.countdownClipAudioId);
            this.countdownClipAudioId = 0;
        }
    }

    lastCollisionTime = Date.now();
    playCollisionSound(volume: number = 0.5) {
        if (this.soundVolume == 0) return;
        let nowMill = Date.now();
        if (nowMill - this.lastCollisionTime < 200) return;
        this.lastCollisionTime = nowMill;
        LoaderTools.loadRes<cc.AudioClip>("sound", "wood_collision").then((clip) => {
            this.audioSource.clip = clip;
            this.audioSource.volume = volume;
            this.audioSource.play();
        });
    }


    setMusicVolume(flag: number) {

        flag = clamp01(flag);
        this.musicVolume = flag;
        cc.audioEngine.setMusicVolume(this.musicVolume);
    }

    setSoundVolume(flag: number) {
        this.soundVolume = flag;
    }

    openMusic() {
        this.setMusicVolume(1);
    }

    closeMusic() {
        this.setMusicVolume(0);
    }

    openSound() {
        this.setSoundVolume(1);
    }

    closeSound() {
        this.setSoundVolume(0);
    }

    vibrate(duration: number, amplitude: number) {
        if (!this.vibrateOpen) return;
        amplitude = amplitude > 255 ? 255 : amplitude;
        amplitude = amplitude <= 0 ? 1 : amplitude;
        duration = duration <= 0 ? 1 : duration;
        App.platform.vibrate(duration, amplitude);
    }
}
