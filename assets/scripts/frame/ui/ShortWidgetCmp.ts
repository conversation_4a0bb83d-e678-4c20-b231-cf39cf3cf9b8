import { AdapationUtil } from "../utils/AdaptionUtil";

const { ccclass, property, requireComponent } = cc._decorator;

@ccclass
@requireComponent(cc.Widget)
export default class ShortWidgetCmp extends cc.Component {
    @property({
        visible() {
            return this.node.getComponent(cc.Widget).isAlignTop;
        }
    })
    top: number = 0;
    @property({
        visible() {
            return this.node.getComponent(cc.Widget).isAlignBottom;
        }
    })
    bottom: number = 0;
    @property({
        visible() {
            return this.node.getComponent(cc.Widget).isAlignLeft;
        }
    })
    left: number = 0;
    @property({
        visible() {
            return this.node.getComponent(cc.Widget).isAlignRight;
        }
    })
    right: number = 0;

    protected onLoad(): void {
        if (this.node.getComponent(cc.Widget)) {
            const widget = this.node.getComponent(cc.Widget);

            if (widget.isAlignTop && this.top)
                widget.top = AdapationUtil.getCustomAdaption(this.top, widget.top);
            if (widget.isAlignBottom && this.bottom)
                widget.bottom = AdapationUtil.getCustomAdaption(this.bottom, widget.bottom);
            if (widget.isAlignLeft && this.left)
                widget.left = AdapationUtil.getCustomAdaption(this.left, widget.left);
            if (widget.isAlignRight && this.right)
                widget.right = AdapationUtil.getCustomAdaption(this.right, widget.right);

            widget.updateAlignment();
        }
    }

}
