import { uiManager } from "./UIManager";
import { UINode } from "./UINode";

const { ccclass, property } = cc._decorator;

/** 界面展示类型 */
export enum UIShowTypes {
    UIFullScreen,       // 全屏显示，全屏界面使用该选项可获得更高性能
    UIAddition,         // 叠加显示，性能较差
    UISingle,           // 单界面显示，只显示当前界面和背景界面，性能较好
};

@ccclass
export class UIView extends cc.Component {

    /** 快速关闭 */
    @property
    quickClose: boolean = false;
    @property({ tooltip: '界面展示动画' })
    openAni: boolean = false;
    @property({ tooltip: '界面关闭动画' })
    closeAni: boolean = false;
    @property(cc.Node)
    AlertNode: cc.Node = null;
    // @property(cc.Node)
    // maskNode: cc.Node = null;
    /** 屏蔽点击选项 在UIConf设置屏蔽点击*/
    // @property
    // preventTouch: boolean = true;
    /** 缓存选项 */
    @property
    cache: boolean = false;
    /** 界面显示类型 */
    @property({ type: cc.Enum(UIShowTypes) })
    showType: UIShowTypes = UIShowTypes.UISingle;
    /** 界面id */
    public uiId: number = 0;
    /**  静态变量，用于区分相同界面的不同实例 */
    private static uiIndex: number = 0;

    private _uiNode: UINode;
    private oMaskOpacity: number = null;

    public tragetScale: number = 1;

    get uiNode(): UINode {
        if (this._uiNode == null) {
            this._uiNode = new UINode(this.node);
        }
        return this._uiNode;
    }

    /********************** UI的回调 ***********************/
    /**
     * 当界面被创建时回调，生命周期内只调用
     * @param args 可变参数
     */
    public init(...args): void {

    }

    public onBaseOpen(fromUI: number, uiArgs: any) {
        if (this.openAni) {
            this.openTween(this.AlertNode);
        }
        this.onOpen(fromUI, uiArgs);
    }

    /**
     * 当界面被打开时回调，每次调用Open时回调
     * @param fromUI 从哪个UI打开的
     * @param args 可变参数
     */
    public onOpen(fromUI: number, ...args): void {

    }

    /**
     * 当界面被打开时回调，每次调用Open时回调，从onOpen中摘离以专门执行Open动画
     */
    public onOpenAni(): void {
        if (this.AlertNode) {
            this.openTween(this.AlertNode);
            // if (this.maskNode) {
            //     if (!this.oMaskOpacity) this.oMaskOpacity = this.maskNode.opacity;
            //     cc.tween(this.maskNode)
            //         .set({ opacity: 1 })
            //         .to(0.2, { opacity: this.oMaskOpacity })
            //         .start();
            // }
        }
    }

    /**
     * 每次界面Open动画播放完毕时回调
     */
    public onOpenAniOver(): void {
    }

    /**
     * 当界面被关闭时回调，每次调用Close时回调
     * 返回值会传递给下一个界面
     */
    public onClose(): any {

    }

    /**
     * 当界面被置顶时回调，Open时并不会回调该函数
     * @param preID 前一个ui
     * @param args 可变参数，
     */
    public onTop(preID: number, ...args): void {

    }

    public onBtn_close() {
        if (this.closeAni) {
            this.closeTween(this.AlertNode);
            // if (this.maskNode) {
            //     cc.tween(this.maskNode)
            //         .to(0.35, { opacity: 1 })
            //         .start();
            // }
        } else {
            uiManager.close(this);
        }
    }

    openTween(node?: cc.Node) {
        this.node.opacity = 0;
        cc.tween(this.node).to(0.15, { opacity: 255 }, { easing: 'sineOut' }).start();
        if (node) {
            node.scale = this.tragetScale * 0.85;
            let tween1 = cc.tween(node).to(0.2, { scale: this.tragetScale * 1.05 }, { easing: 'sineOut' });
            let tween4 = cc.tween(node).to(0.15, { scale: this.tragetScale }, { easing: 'sineOut' })
            cc.tween(node)
                .sequence(tween1, tween4)
                .start();
        }

    }

    closeTween(node?: cc.Node) {
        cc.tween(this.node).to(0.2, { opacity: 0 }, { easing: 'sineOut' }).call(() => {
            uiManager.close(this);
        }).start();
        if (node) {
            cc.tween(node).to(0.2, { scale: this.tragetScale * 0.85 }, { easing: 'sineOut' }).start();
        }
    }
}
