import { UIView, UIShowTypes } from "./UIView";
import { uiLayers } from "./UILayers";
import Node = cc.Node;
import director = cc.director;
import instantiate = cc.instantiate;
import Event = cc.Event;
import isValid = cc.isValid;
import Widget = cc.Widget;
import Utils from "../utils/Utils";
import { LoaderTools } from "../utils/LoaderTools";
import { LogUtil } from "../utils/LogUtil";
import { UIID } from "../const/UIID";

/**
 * UIManager界面管理类
 * 
 * 1.打开界面，根据配置自动加载界面、调用初始化、播放打开动画、隐藏其他界面、屏蔽下方界面点击
 * 2.关闭界面，根据配置自动关闭界面、播放关闭动画、恢复其他界面
 * 3.切换界面，与打开界面类似，但是是将当前栈顶的界面切换成新的界面（先关闭再打开）
 * 4.提供界面缓存功能
 *
 */

/** UI栈结构体 */
export interface UIInfo {
    uiId: number;
    uiView: UIView;
    uiArgs: any;
    zOrder?: number;
    openType?: 'quiet' | 'other';
    isClose?: boolean;
    resToClear?: string[];
    resCache?: string[];
    queen?: boolean;
    priority?: number;
}

/** UI配置结构体 */
export interface UIConf {
    prefab: string;
    bundle?: string;
    preventTouch?: boolean;
}

export enum ScreenType {
    Long,       //长屏
    Short,      //短屏
    Pad         //平板
}

export class UIManager {
    /** 资源加载计数器，用于生成唯一的资源占用key */
    private useCount = 0;

    /** 是否正在打开UI */
    // private isOpening = false;
    // private openingUIId: number;

    /** UI界面缓存（key为UIId，value为UIView节点）*/
    private UICache: { [UIId: number]: UIView } = {};
    /** UI界面栈（{UIID + UIView + UIArgs}数组）*/
    private UIStack: UIInfo[] = [];
    /** UI待打开列表 */
    private UIOpenQueue: UIInfo[] = [];

    /** UI待关闭列表 */
    // private UICloseQueue: UIView[] = [];
    /** UI配置 */
    private UIConf: { [key: number]: UIConf } = {};

    /** 所有UI层级 */
    private layers: Map<string, Node> = new Map<string, Node>();
    /** UI Root Canvas */
    private uiRootCanvasNode: Node;

    /** UI打开前回调 */
    public uiOpenBeforeDelegate: (uiId: number, preUIId: number) => void = null;
    /** UI打开回调 */
    public uiOpenDelegate: (uiId: number, preUIId: number) => void = null;
    /** UI关闭回调 */
    public uiCloseDelegate: (uiId: number) => void = null;

    public screenType: ScreenType = ScreenType.Long;

    /**
     * 初始化所有UI的配置对象
     * @param conf 配置对象
     */
    public initUIConf(conf: { [key: number]: UIConf }): void {
        this.UIConf = conf;

        let winSize = cc.view.getFrameSize();
        let resolutionSize = cc.view.getDesignResolutionSize();

        if (winSize.height / winSize.width < 1.77) {
            // 平板
            this.screenType = ScreenType.Pad;
        } else if (winSize.height / winSize.width < 2) {
            // 短手机
            this.screenType = ScreenType.Short;
        } else {
            // 长屏幕手机
            this.screenType = ScreenType.Long;
        }
    }

    /**
     * 设置或覆盖某uiId的配置
     * @param uiId 要设置的界面id
     * @param conf 要设置的配置
     */
    public setUIConf(uiId: number, conf: UIConf): void {
        this.UIConf[uiId] = conf;
    }

    /****************** 私有方法，UIManager内部的功能和基础规则 *******************/

    /**
     * 添加防触摸层
     * @param zOrder 屏蔽层的层级
     */
    private preventTouch(zOrder: number) {
        let child = director.getScene().getChildByName('Canvas') as unknown as Node;
        let node = Utils.addNode(child, 'preventTouch')
        node.on(Node.EventType.TOUCH_START, function (event: Event) {
        }, node);
        child.addChild(node);
        return node;
    }

    /** 自动执行下一个待关闭或待打开的界面 */
    private autoExecNextUI() {
        // 逻辑上是先关后开
        // if (this.UICloseQueue.length > 0) {
        //     let uiQueueInfo = this.UICloseQueue[0];
        //     this.UICloseQueue.splice(0, 1);
        //     this.close(uiQueueInfo);
        // } else 
        if (this.UIOpenQueue.length > 0) {
            let uiInfo = this.UIOpenQueue[0];
            this.openFromInfo(uiInfo);
        }
    }

    /**
     * 自动检测动画组件以及特定动画，如存在则播放动画，无论动画是否播放，都执行回调
     * @param aniName 动画名
     * @param aniOverCallback 动画播放完成回调
     */
    private autoExecAnimation(uiView: UIView, aniName: string, aniOverCallback: () => void) {
        // 暂时先省略动画播放的逻辑
        if (uiView.getComponent(cc.Animation)) {
            let ani = uiView.getComponent(cc.Animation);
            ani.play(aniName);
            ani.on('stop', () => {
                aniOverCallback();
            }, this);
        } else {
            aniOverCallback();
        }
    }

    /**
     * 自动检测资源预加载组件，如果存在则加载完成后调用completeCallback，否则直接调用
     * @param completeCallback 资源加载完成回调
     */
    private async autoLoadRes(uiView: UIView) {

    }

    /** 生成唯一的资源占用key */
    private makeUseKey(): string {
        return `UIMgr_${++this.useCount}`;
    }

    /** 根据界面显示类型刷新显示 */
    private updateUI() {

    }

    /**
     * 异步加载一个UI的prefab，成功加载了一个prefab之后
     * @param uiId 界面id
     * @param processCallback 加载进度回调
     * @param completeCallback 加载完成回调
     * @param uiArgs 初始化参数
     */
    private async getOrCreateUI(uiId: number, uiArgs: any): Promise<UIView> {
        // 如果找到缓存对象，则直接返回
        let uiView: UIView = this.UICache[uiId];
        if (uiView) {
            return uiView;
        }

        // 找到UI配置
        if (!this.UIConf[uiId]) {
            LogUtil.error(`getOrCreateUI ${uiId} faile`);
            return null;
        }
        let uiPath = this.UIConf[uiId].prefab;
        if (null == uiPath) {
            LogUtil.print(`getOrCreateUI ${uiId} faile, prefab conf not found!`);
            return null;
        }
        let prefab = await LoaderTools.loadRes<cc.Prefab>(this.UIConf[uiId].bundle, this.UIConf[uiId].prefab);
        if (!prefab) return null;
        let uiNode: Node = instantiate(prefab);
        uiView = uiNode.getComponent(UIView);
        if (!uiView) {
            LogUtil.print(`getOrCreateUI getComponent ${uiId} faile, path: ${uiPath}`);
            uiNode.destroy();
            return null;
        }
        uiView.uiId = uiId;
        await this.autoLoadRes(uiView);   // 异步加载UI预加载的资源
        uiView.init(uiArgs);
        return uiView;
    }

    /**
     * UI被打开时回调，对UI进行初始化设置，刷新其他界面的显示，并根据
     * @param uiId 哪个界面被打开了
     * @param uiView 界面对象
     * @param uiInfo 界面栈对应的信息结构
     * @param uiArgs 界面初始化参数
     */
    private onUIOpen(uiInfo: UIInfo) {
        if (!uiInfo.uiView) return;
        // 激活界面

        let uiView = uiInfo.uiView;
        uiView.node.active = true;
        // uiView.node.zIndex = uiInfo.zOrder || this.UIStack.length;

        // 快速关闭界面的设置，绑定界面中的background，实现快速关闭
        if (uiView.quickClose) {
            let backGround = uiView.node.getChildByName('background');
            if (!backGround) {
                Utils.addNode(uiView.node, 'background');
            }
            backGround.targetOff(Node.EventType.TOUCH_START);
            backGround.on(Node.EventType.TOUCH_START, (event: Event) => {
                this.close(uiView);
            }, backGround);
        }

        // 添加到场景中
        // let child = director.getScene().getChildByName('Canvas');
        let nodeName = ''
        switch (uiView.showType) {
            case UIShowTypes.UIFullScreen:
                nodeName = 'bottom';
                break;
            case UIShowTypes.UIAddition:
                nodeName = 'middle';
                break;
            case UIShowTypes.UISingle:
                nodeName = 'top';
                break;
            default:
                break;
        }
        let parent = director.getScene().getChildByName('Canvas').getChildByName(nodeName);
        uiView.node.setParent(parent);
        uiView.node.setPosition(cc.v2());

        // if (GlobalConfig.isDebug) {
        //     let time = new Date().toLocaleString();
        //     Log.print("%c[open-view %s]>>> %s", "color:blue", time, uiInfo.uiView.name);
        // }

        // 刷新其他UI
        this.updateUI();

        // 从那个界面打开的
        let fromUIID = 0;
        if (this.UIStack.length > 1) {
            fromUIID = this.UIStack[this.UIStack.length - 2].uiId
        }

        // 打开界面之前回调
        if (this.uiOpenBeforeDelegate) {
            this.uiOpenBeforeDelegate(uiInfo.uiId, fromUIID);
        }

        // 执行onOpen回调
        uiView.onBaseOpen(fromUIID, uiInfo.uiArgs);
        this.autoExecAnimation(uiView, "uiOpen", () => {
            uiView.onOpenAniOver();
            if (this.uiOpenDelegate) {
                this.uiOpenDelegate(uiInfo.uiId, fromUIID);
            }
        });
    }

    public openQueue(uiId: number, priority: number = 0, uiArgs: any = null) {
        let uiInfo: UIInfo = {
            uiId: uiId,
            uiArgs: uiArgs,
            uiView: null,
            queen: true,
            priority: priority,
        };
        let i = 0;
        for (; i < this.UIOpenQueue.length; i++) {
            if (this.UIOpenQueue[i].priority < priority) {
                break;
            }
        }
        this.UIOpenQueue.splice(i, 0, uiInfo);  // 根据优先级插入数据
        if (this.UIOpenQueue.length == 1) {
            this.autoExecNextUI();
        }
    }

    /** 打开界面并添加到界面栈中 */
    public async open(uiId: number, uiArgs: any = null) {
        let info = this.getUIInfo(uiId);
        if (info != null) { // 重复打开了同一个界面
            if (!info.uiView) {
                LogUtil.print(`open ${uiId} fail! this ui in opening!`);
                return;
            }
            if (this.getTopUIIndex() == uiId) {
                LogUtil.print(`open ${uiId} fail! topUI is this!`);
                return
            } else {
                this.close(info.uiView);
                return;
            }
        }
        let uiInfo: UIInfo = {
            uiId: uiId,
            uiArgs: uiArgs,
            uiView: null,
        };
        await this.openFromInfo(uiInfo);
    }

    public async openFromInfo(uiInfo: UIInfo) {
        let uiId = uiInfo.uiId;
        let uiArgs = uiInfo.uiArgs;
        uiInfo.zOrder = (uiArgs && uiArgs.zOrder) || this.UIStack.length + 1;  // 设置UI的zOrder
        let info = this.getUIInfo(uiId);
        if (info != null) { // 重复打开了同一个界面
            if (!info.uiView) {
                LogUtil.print(`open ${uiId} fail! this ui in opening!`);
                return;
            }
            if (this.getTopUIIndex() == uiId) {
                LogUtil.print(`open ${uiId} fail! topUI is this!`);
                return
            } else {
                this.close(info.uiView);
                return;
            }
        }
        this.UIStack.push(uiInfo);

        let uiView = await this.getOrCreateUI(uiId, uiArgs);
        if (!uiView) {
            LogUtil.print(`getOrCreateUI ${uiId} faile! close state : ${uiInfo.isClose} , uiView : ${uiView}`);
            return;
        }
        uiInfo.uiView = uiView;
        // 打开UI，执行配置
        // 逻辑层函数异常处理,防止卡主ui queue
        try {
            if (uiInfo.isClose) return;
            this.onUIOpen(uiInfo);
            if (uiInfo.uiArgs != null && uiInfo.uiArgs.onOpenCallback != null) {
                uiInfo.uiArgs.onOpenCallback(uiView);
            }
        } catch (error) {
            LogUtil.error(error)
        }
    }

    /** 替换栈顶界面 */
    public replace(uiId: number, uiArgs: any = null) {
        this.close(this.UIStack[this.UIStack.length - 1].uiView);
        this.open(uiId, uiArgs);
    }

    /**
     * 根据uiid关闭界面
     * @param uiId 
     */
    public closeById(uiId: number) {
        let uiView = this.getUI(uiId);
        if (uiView) {
            this.close(uiView);
        }
    }

    /**
     * 关闭当前界面
     * @param closeUI 要关闭的界面
     */
    public close(closeUI?: UIView) {
        let uiInfo: UIInfo;
        if (closeUI) {
            for (let index = this.UIStack.length - 1; index >= 0; index--) {
                let ui = this.UIStack[index];
                if (ui.uiView === closeUI) {
                    uiInfo = ui;
                    this.UIStack.splice(index, 1);
                    break;
                }
            }
        } else {
            uiInfo = this.UIStack.pop();
        }

        let uiView = closeUI;
        let uiId = uiView?.uiId;
        if (uiInfo) {
            uiId = uiInfo.uiId;
            uiView = uiInfo.uiView;
            uiInfo.isClose = true;
        }
        if (!uiView) {
            return;
        }

        // 处理显示模式
        this.updateUI();
        let close = () => {
            uiView.onClose();
            if (this.uiCloseDelegate) {
                this.uiCloseDelegate(uiId);
            }
            if (uiView.cache) {
                this.UICache[uiId] = uiView;
                uiView.node.removeFromParent();
                LogUtil.print(`uiView removeFromParent ${uiId}`);
            } else {
                uiView.node.destroy();
            }
            if (uiInfo?.queen) {
                for (let i = 0; i < this.UIOpenQueue.length; i++) {
                    if (this.UIOpenQueue[i] == uiInfo) {
                        this.UIOpenQueue.splice(i, 1);  // close的时候再移除
                        break;
                    }
                }
                this.autoExecNextUI();
            }
        }
        // 执行关闭动画
        this.autoExecAnimation(uiView, "uiClose", close);
    }

    /** 关闭所有界面 */
    public closeAll() {
        // 不播放动画，也不清理缓存
        for (const uiInfo of this.UIStack) {
            uiInfo.isClose = true;
            if (uiInfo.uiView) {
                uiInfo.uiView.onClose();
                uiInfo.uiView.node.destroy();
            }
        }
        this.UIOpenQueue = [];
        this.UIStack = [];
    }

    /** 清理界面缓存 */
    public clearCache(): void {
        for (const key in this.UICache) {
            let ui = this.UICache[key];
            if (isValid(ui.node)) {
                ui.node.destroy();
            }
        }
        this.UICache = {};
    }

    /******************** UI的便捷接口 *******************/
    public isTopUI(uiId): boolean {
        if (this.UIStack.length == 0) {
            return false;
        }
        return this.UIStack[this.UIStack.length - 1].uiId == uiId;
    }

    public getUI(uiId: number): UIView {
        for (let index = 0; index < this.UIStack.length; index++) {
            const element = this.UIStack[index];
            if (uiId == element.uiId) {
                return element.uiView;
            }
        }
        return null;
    }

    public getUIInfo(uiId: number): UIInfo {
        for (let index = 0; index < this.UIStack.length; index++) {
            const element = this.UIStack[index];
            if (uiId == element.uiId) {
                return element;
            }
        }
        return null;
    }


    public getTopUI(): UIView {
        if (this.UIStack.length > 0) {
            return this.UIStack[this.UIStack.length - 1].uiView;
        }
        return null;
    }

    public getTopUIIndex(): number {
        if (this.UIStack.length > 0) {
            return this.UIStack[this.UIStack.length - 1].uiId;
        }
        return -1;
    }

    public getUIIndex(uiId: number): number {
        for (let index = 0; index < this.UIStack.length; index++) {
            const element = this.UIStack[index];
            if (uiId == element.uiId) {
                return index;
            }
        }
        return -1;
    }

    public getShowNum(): number {
        return this.UIStack.length;
    }

    public hasShown(uiId: number): boolean {
        return this.getUIIndex(uiId) >= 0 || this.inQueue(uiId);
    }

    inQueue(uiId: number): boolean {
        for (let i = 0; i < this.UIOpenQueue.length; i++) {
            if (this.UIOpenQueue[i].uiId == uiId) {
                return true;
            }
        }
        return false;
    }

    public initialize(uiConf: any, uiConfMain: any) {
        let CF = {}
        if (uiConfMain) {
            CF = Object.assign(uiConf, uiConfMain);
        }
        this.initUIConf(CF);

        this.layers = new Map<string, Node>();
        this.uiRootCanvasNode = director.getScene().getChildByName('Canvas');
        let designSize = this.uiRootCanvasNode.getComponent(cc.Canvas).designResolution;

        uiLayers.forEach((layer) => {
            if (this.layers[layer.layerName] == null) {
                let layerNode = new Node(layer.layerName);
                this.layers[layer.layerName] = layerNode;
                let widget = layerNode.addComponent(Widget);
                this.uiRootCanvasNode.addChild(layerNode);
                layerNode.setContentSize(designSize);
                widget.isAlignLeft = true;
                widget.isAlignRight = true;
                widget.isAlignBottom = true;
                widget.isAlignTop = true;
            }
        });
    }

    // public addAudioRoot() {
    //     //音效节点
    //     let audioNode = new Node("audioNode");
    //     audioNode.addComponent(AudioSource)
    //     audioNode.addComponent(audioRoot)
    //     this.uiRootCanvasNode.addChild(audioNode)

    // }

    public async tip(describe: string = "", duration: number = 0.5, colorStr: string = "#ffffff") {
        let conf = this.UIConf[UIID.UITip];
        if (!conf) {
            LogUtil.error(`tip config faile`);
            return
        }
        let pre = await LoaderTools.loadRes<cc.Prefab>(conf.bundle, conf.prefab);
        let node = cc.instantiate(pre);
        let parent = director.getScene().getChildByName('Canvas').getChildByName("top");
        node.setParent(parent);
        cc.find("pnl_tips/rick_text", node).getComponent(cc.RichText).string = "<color=" + colorStr + ">" + describe + "</color>";
        cc.tween(node).set({position:cc.v3(), opacity:0}).to(0.2, {opacity:255}).to(duration, { position: cc.v3(0, 0)}).call(() => {
            node.removeFromParent();
            node.destroy();
        }).start();
    }

}

export let uiManager: UIManager = new UIManager();
