import { AesManager } from "../http/AesManager";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { BaseModel } from "./BaseModel";

export enum DeviceType {
    Default,
    Low,
    Middle,
    High
}

export default class DeviceInfo extends BaseModel {
    public appver: string = "";                 //版本
    public appver_name: string = "";            //版本号
    public did: string = "";                    //设备id
    public pkg: string = "";                    //正式包名
    public gaid: string = "";                   //GP广告Id
    public brd: string = "";                    //手机品牌
    public aid: string = "";                    //设备的Android Id
    public country: string = "";                //国家代码
    public language: string = "";               //语言
    public timezone: Number = -8;               //时区
    public os_version: string = "";             //客户端操作系统版本
    public mod: string = "";                    //手机型号
    public idfa: string = "";
    public idfv: string = "";
    public channel: string = "";                 //sdk渠道
    public sub_channel: string = "";             //子渠道
    public install_info: string = "";            //安装信息
    public buildIndex: string = "0";             //打包次数
    public bucket: string = "1";                 //bucket
    public pf: string = "android";
    public total_memory: string = "8192";
    public fcmToken: string = "";

    public get version() {
        return this.appver_name;
    }

    public get loginCountry() {
        let countryStr = LocalStorageMgr.getStringValue("wood_loginCountry", "");
        if (countryStr == "") {
            countryStr = this.country;
            this.loginCountry = countryStr;
        }
        return countryStr;
    }

    public set loginCountry(v) {
        LocalStorageMgr.setStringValue("wood_loginCountry", v);
    }

    public get gameToken() {
        let str = this.aid + ";" + this.loginCountry + ";" + this.pkg + ";" + this.version;
        return AesManager.encrypt(str);
    }


    private deviceType: DeviceType = DeviceType.Default;
    public getDeviceType(): DeviceType {
        if (this.deviceType != DeviceType.Default)
            return this.deviceType;
        if (!this.total_memory || this.total_memory == "") {
            this.deviceType = DeviceType.High;
            return this.deviceType;
        }
        let mem = Number(this.total_memory);
        if (mem > 0 && mem < 1024 * 4) {
            this.deviceType = DeviceType.Low;
        } else if (mem >= 1024 * 4 && mem < 1024 * 6) {
            this.deviceType = DeviceType.Middle;
        } else {
            this.deviceType = DeviceType.High;
        }
        return this.deviceType;
    }


    public IsTestPack(): boolean {
        console.log("IsTestPack: ", this.pkg);
        let debug: boolean = this.pkg != "com.coloring.sort.paint";
        return debug;
    }

    public get isA() {
        return Number(this.bucket) % 2 == 0;
    }
}