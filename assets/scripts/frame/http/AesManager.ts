import * as CryptoJS from 'crypto-js'

export class AesManager {

    static get encryptKey() {
        return CryptoJS.enc.Utf8.parse('IgnalX71IEf456PT');
    }
    static get decryptKey() {
        return  CryptoJS.enc.Utf8.parse('IgmatX71IEf456PT');
    }

    static get encryptClientKey() {
        return CryptoJS.enc.Utf8.parse('IgkibX71IEf456PT');
    }

    static get decryptClientKey() {
        return CryptoJS.enc.Utf8.parse('IgkibX71IEf456PT');
    }

    static get encryptLogKey() {
        return CryptoJS.enc.Utf8.parse('jx3bu9dxvv6bqi4s');
    }

    static get decryptLogKey() {
        return CryptoJS.enc.Utf8.parse('jx3bu9dxvv6bqi4s');
    }

    /* 加密 */
    static encrypt(word: any) {
        const srcs = CryptoJS.enc.Utf8.parse(word)
        const encrypted = CryptoJS.AES.encrypt(srcs, this.encryptKey, { iv: this.encryptKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
        return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
        // return encrypted.ciphertext;
    }

    /* 解密 */
    static decrypt(word: any) {
        const decrypt = CryptoJS.AES.decrypt(word, this.decryptKey, { iv: this.decryptKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
        const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
        return decryptedStr.toString()
    }

    /* 加密 */
    static encryptClient(word: any) {
        const srcs = CryptoJS.enc.Utf8.parse(word)
        const encrypted = CryptoJS.AES.encrypt(srcs, this.encryptClientKey, { iv: this.encryptClientKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
        return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
    }

    /* 解密 */
    static decryptClient(word: any) {
        const decrypt = CryptoJS.AES.decrypt(word, this.decryptClientKey, { iv: this.encryptClientKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
        const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
        return decryptedStr.toString()
    }

    /* 加密Log */
    static encryptLog(word: any) {
        const srcs = CryptoJS.enc.Utf8.parse(word);
        const encrypted = CryptoJS.AES.encrypt(srcs, this.encryptLogKey, { iv: this.encryptLogKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.ZeroPadding });
        return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
    }

    /* 解密Log */
    static decryptLog(word: any) {
        const decrypt = CryptoJS.AES.decrypt(word, this.encryptLogKey, { iv: this.encryptLogKey, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.ZeroPadding });
        const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
        return decryptedStr.toString();
    }
}