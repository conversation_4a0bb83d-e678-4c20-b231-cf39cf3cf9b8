
import { LogUtil } from "../utils/LogUtil";
import { AesManager } from "./AesManager";

var IsDecode: boolean = true;

export default class HttpMgr {
    private static _ins: HttpMgr;
    public static get ins(): HttpMgr { return this._ins = this._ins || new HttpMgr() }

    doGet(url: string, data?: any, complete?: Function, error?: Function, headers?: { name: string, value: string }) {
        let sendData: string = IsDecode ? this.convertEncryptData(data) : this.convertData(data);
        //let paramStr: string = sendData != "" ? "?" + sendData : "";

        try {
            let request: XMLHttpRequest = this.getRequest("GET", url, complete, error, headers);
            request.send(sendData);
            let i = 0;
        } catch (err) {
            LogUtil.print("doGet err==>", err);
            if (error)
                error(err)
        }
    }

    doPost(url: string, data?: any, headers?: { name: string, value: string }, complete?: Function, error?: Function) {
        let sendData;
        if (url.includes("addlogs")) {
            sendData = this.convertData(data);
        } else {
            sendData = IsDecode ? this.convertEncryptData(data) : this.convertData(data);
        }
        try {
            let request: XMLHttpRequest = this.getRequest("POST", url, complete, error, headers);
            request.send(sendData);
            let i = 0;
        } catch (err) {
            LogUtil.print("doPost err==>", err);
            if (error)
                error(err)
        }

    }

    private converWebData(data: any): string {
        if (data) {
            var t = [];
            for (var n in data)
                t.push(encodeURIComponent(n) + "=" + encodeURIComponent(data[n]));
            return t.join("&")
        }
    }

    private convertData(data: any): string {
        var result: string = "";
        if (data) {
            for (var key in data) {
                result += (key + "=" + this.encode(data[key])) + "&";
            }
            var len: number = result.length;
            len > 0 && (result = result.substring(0, len - 1));
        }
        return result;
    }

    private convertEncryptData(data: any): any {
        var result: string = JSON.stringify(data);
        var enCodeStr = AesManager.encrypt(result);
        return enCodeStr;
    }

    private encode(str: string): string {
        var result: string = encodeURIComponent(str);
        result = result.replace(/\+/g, "%2B");
        return result;
    }

    doPostLog(url: string, data?: any, headers?: { name: string, value: string }, complete?: Function, error?: Function, isWebMobile?: boolean) {
        let sendData = isWebMobile ? this.converWebData(data) : this.convertData(data);
        try {
            let request: XMLHttpRequest = this.getLogRequest("POST", url, complete, error, headers);
            request.send(sendData);
        } catch (err) {
            LogUtil.print("doPost err==>", err);
            if (error)
                error(err)
        }
    }

    private getLogRequest(method: string, url: string | URL, complete: Function, error: Function, header?: { name: string, value: string }): XMLHttpRequest {
        let xhr = new XMLHttpRequest();
        xhr.open(method, url);
        if (header) {
            xhr.setRequestHeader(header.name, header.value);
        }
        xhr.onreadystatechange = () => {
            if (xhr.readyState < 4) {
                return;
            }
            if (xhr.status >= 200 && xhr.status < 400) {
                let response = xhr.responseText;
                LogUtil.print("response==>" + response);
                try {
                    let res = JSON.parse(response)
                    if (complete) {
                        complete(res);
                    }
                } catch (err) {
                    if (error) {
                        error(err)
                    }
                }
            }
        }
        xhr.timeout = 10000;
        xhr.ontimeout = () => {
            LogUtil.print("timeout==>" + url);
            if (error) {
                error("ontimeout");
            }
        }
        xhr.onerror = (err) => {
            LogUtil.print(url + " error==>", err);
            if (error) {
                error(err)
            }
        };
        return xhr;
    }

    private getRequest(method: string, url: string | URL, complete: Function, error: Function, header?: { name: string, value: string }): XMLHttpRequest {
        let xhr = new XMLHttpRequest();
        xhr.open(method, url);
        if (header) {
            xhr.setRequestHeader(header.name, header.value);
        }
        if (method == "POST")
            xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = () => {
            if (xhr.readyState < 4) {
                return;
            }
            if (xhr.status >= 200 && xhr.status < 400) {
                let response;
                response = IsDecode ? AesManager.decrypt(xhr.responseText) : xhr.responseText;

                if (url.toString().indexOf("ping") > -1 || url.toString().includes("addlogs")) {
                    response = xhr.responseText;
                }
                LogUtil.print("response==>" + response);
                try {
                    let res = JSON.parse(response)
                    if (complete) {
                        complete(res);
                    }
                } catch (err) {
                    if (error) {
                        error(err)
                    }
                }
            }
        }

        xhr.timeout = 10000;

        xhr.ontimeout = () => {
            LogUtil.print("timeout==>" + url);
            if (error) {
                error("ontimeout");
            }
        }

        xhr.onerror = (err) => {
            LogUtil.print(url + " error==>", err);
            if (error) {
                error(err)
            }
        };

        return xhr;
    }
}