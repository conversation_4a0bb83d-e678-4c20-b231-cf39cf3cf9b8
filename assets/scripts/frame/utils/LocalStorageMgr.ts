

export class LocalStorageMgr {
    static getValue(key: string, defaultValue: any): number {
        let value = defaultValue;
        let valueStr = localStorage.getItem(key);
        if (valueStr == null) {
            valueStr = `${defaultValue}`;
            localStorage.setItem(key, valueStr);
        } else {
            value = Number(valueStr);
        }
        return value;
    }

    static setValue(key: string, value: any) {
        localStorage.setItem(key, `${value}`);
    }

    static getStringValue(key: string, defaultValue: any): string {
        let value = defaultValue;
        let valueStr = localStorage.getItem(key);
        if (valueStr == null) {
            valueStr = `${defaultValue}`;
            localStorage.setItem(key, valueStr);
        } else {
            value = valueStr;
        }
        return value;
    }

    static setStringValue(key: string, value: any) {
        localStorage.setItem(key, value);
    }

    static setObject(key: string, value) {
        localStorage.setItem(key, JSON.stringify(value));
    }

    static getObject(key: string) {
        let valueStr = localStorage.getItem(key);

        return (this.getJSON(valueStr) || {});
    }

    static getJSON(str) {
        if (typeof str == 'string') {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == 'object' && obj) {
                    return obj;
                } else {
                    return null;
                }

            } catch (e) {
                return null;
            }
        }
    }

    //获取每天需要重置的数据
    static getDailyValue(key: string, defaultValue: number): number {
        let value = this.getValue(key, defaultValue);
        let lastTimestamp = LocalStorageMgr.getValue(key + "_ts", 0);
        if (!this.isTodayDay(lastTimestamp)) {
            LocalStorageMgr.setDailyValue(key, defaultValue);
        }
        return value;
    }

    static setDailyValue(key: string, value: any) {
        localStorage.setItem(key, `${value}`);
        let currentTimestamp = new Date().getTime();
        localStorage.setItem(key + "_ts", `${currentTimestamp}`);
    }

    static isTodayDay(lastTimestamp: number): boolean {
        let currentTimestamp = new Date().getTime();
        var isToday = Math.floor(currentTimestamp / (24 * 60 * 60 * 1000)) === Math.floor(lastTimestamp / (24 * 60 * 60 * 1000));
        return isToday;
    }
}
