
export class LogUtil {
    public static logging = true;
    
    public static print(message?: any, ...optionalParams: any) {
        if (this.logging) {
            console.log(message, ...optionalParams);
        }
    }

    public static error(message?: any, ...optionalParams: any) {
        if (this.logging) {
            console.error(message, ...optionalParams);
        }
    }

    public static writeToJson(data: any, tag: string = "normal"): void {
        if (this.logging) {
            this.writeLog(JSON.stringify(data), tag);
        }
    }

    public static writeLog(msg: string, tag: string = "normal"): void {

    }
}