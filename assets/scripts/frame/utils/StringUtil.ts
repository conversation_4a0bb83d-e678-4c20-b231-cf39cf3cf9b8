import Label = cc.Label;

export default class StringUtil {
    /**
     * 
     * @param str  要在其中进行替换的字符串。该字符串可包含 {n} 形式的特殊标记，其中 n 为从零开始的索引，它将被该索引处的其他参数（如果指定）替换。
     * @param rest 可在 str 参数中的每个 {n} 位置被替换的其他参数，其中 n 是一个对指定值数组的整数索引值（从 0 开始）
     * @returns 
     */
    public static substitute(str: string, ...rest): string {
        var len: number = rest ? rest.length : 0;
        if (len <= 0) return str;
        var res: string, tmp: string;
        if (str.indexOf("{coin}") > -1) {
            tmp = res || str;
            res = tmp.replace("{coin}", rest[len - 1])
        }
        for (var i: number = 0; i < len; i++) {
            tmp = res || str;
            res = tmp.replace("{" + i + "}", rest[i]);
        }
        return res;
    }

    /**
     * 时间格式化
     * @param time 毫秒
     * @param format 需要格式化的样式 例如 YYYY-MM-DD hh:mm:ss(注意 月份（MM）和分钟(mm)的区别)
     * @returns 
     */
    public static formatDate(time: number, format: string): string {
        let date = new Date(time);
        let year = date.getFullYear().toString();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        let str = format.replace(/yyyy|YYYY/, year);
        str = str.replace(/MM/, month > 9 ? month.toString() : "0" + month);
        str = str.replace(/dd|DD/, day > 9 ? day.toString() : "" + (day || ""));
        str = str.replace(/hh|HH/, hour > 9 ? hour.toString() : "0" + hour);
        str = str.replace(/mm/, minute > 9 ? minute.toString() : "0" + minute);
        str = str.replace(/ss|SS/, second > 9 ? second.toString() : "0" + second);
        return str;
    }

    public static trim(string) {
        if (string.trim) {
            return string.trim();
        } else {
            let reg = /^\s+|\s+$/g;
            return string.replace(reg, "");
        }
    }

    public static FormatMoney(num: number, fixedNum: number = 2) {
        if (num < 10000) {
            //return num.toString().replace(/(\d)(?=(\d{3})+$)/, (e) => {
            return `${num}`;
            //});
        } else {
            let str = "";
            if (num >= 10000 && num < 1000000) {
                str = (num / 1000).toFixed(fixedNum) + "K"
            } else if (num >= 100000 && num < 1000000000) {
                str = (num / 1000000).toFixed(fixedNum) + "M";
            } else {
                str = (num / 1000000000).toFixed(fixedNum) + "B";
            }
            return str;
        }
    }

    public static FormatMoneyDot(num: number) {
        if (num <= 1000) {
            return `${num}`;
        } else {
            let str = "";
            while (num >= 1) {
                let subNum = (num % 1000);
                let haveNext = Math.floor(num / 1000) >= 1;
                if (haveNext) {
                    if (subNum >= 100) {
                        str = `,${subNum}${str}`;
                    } else if (subNum >= 10) {
                        str = `,0${subNum}${str}`;
                    } else if (subNum > 0) {
                        str = `,00${subNum}${str}`;
                    } else if (subNum == 0) {
                        str = `,000${str}`;
                    }
                } else {
                    str = `${subNum}${str}`;
                }

                num = Math.floor(num / 1000);
            }
            return str;
        }
    }

    /**
     * 倒计时格式化
     * @param time 毫秒
     * @param format d 天，h时,m分，s秒
     * @returns 
     */
    public static formatCountDownTime(time: number, format: string = "dd天hh:mm:ss"): string {
        let day = time / (1000 * 60 * 60 * 24) | 0;
        let hour = time / (1000 * 60 * 60) % 24 | 0;
        let minute = time / (1000 * 60) % 60 | 0;
        let second = time / 1000 % 60 | 0;
        let str = format;

        let list = [
            [day, /dd|DD/, /d|D/],
            [hour, /hh|HH/, /h|H/],
            [minute, /mm|MM/, /m|M/],
            [second, /ss|SS/, /s|S/]
        ];
        let len = list.length;
        let arr;
        let regexp;
        let value;
        for (let i = 0; i < len; i++) {
            arr = list[i];
            value = arr[0];
            regexp = arr[1];
            if (str.match(regexp)) {
                if (i == 0) {
                    str = str.replace(regexp, value > 9 ? value.toString() : "" + value);
                } else {
                    str = str.replace(regexp, value > 9 ? value.toString() : "0" + value);
                }
            } else {
                regexp = arr[2];
                str = str.replace(regexp, value.toString());
            }
        }
        return str;
    }

    public static formatCountdown(countDown: number): string {
        let day = Math.floor((countDown) / (24 * 60 * 60 * 1000))
        if (day >= 1) {
            let h = Math.floor((countDown - day * 24 * 60 * 60 * 1000) / (60 * 60 * 1000))
            return day + "d " + h + "h"
        }
        return this.formatCountDownTime(countDown, "hh:mm:ss");
    }

    /**
     * 字符串中是否存在特殊字符
     * @param str 
     * @returns 存在 true
     */
    public static hasSpecialStr(str): boolean {
        var specialChars = "~·`!！@#$￥%^…&* ()（）—-_=+[]{ }【】、|\\;:；：'\"“‘,./<>《》?？，。";
        var len = specialChars.length;
        for (var i = 0; i < len; i++) {
            if (str.indexOf(specialChars.substring(i, i + 1)) != -1) {
                return true;
            }
        }
        return false;
    }

    public static getInputStr(str: string, emoji = false) {
        str = str.replace(/^\s+|\s+$/g, "");
        if (emoji) {
            let emojiReg = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g ///[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
            str = str.replace(emojiReg, ''); // 将所有匹配到的emoji表情符号替换为空字符串
        }
        return str;
    }

    public static replaceLanStr(str: string) {
        let len = str.length;
        let temp = "";
        for (var i: number = 0; i < len; i++) {
            if (str[i] !== "~" && str[i] !== "^") {
                temp = temp + str[i];
            }
        }
        return temp;
    }

    public static getFitString(source: string, text: Label, maxWidth: number): void {
        text.overflow = cc.Label.Overflow.NONE;
        //text.node.width = maxWidth;
        if (source || source == "") {
            text.string = source;
            //@ts-ignore
            text._forceUpdateRenderData(true);
            const strArr = Array.from(source);
            let repeat = (i, len) => {
                let tw;
                let max = Math.min(strArr.length, i);
                let result = strArr.slice(0, max).join("");
                //console.error(result);
                text.string = result + "…";
                //@ts-ignore
                text._forceUpdateRenderData(true);
                tw = text.node.getContentSize().width;
                if (tw > maxWidth) {
                    max = max - 1;
                    let result = strArr.slice(0, max).join("");
                    text.string = result + "…";
                    //text.string = source.substring(0, i - 1) + "…";
                    return
                } else {
                    if (i + 1 < len) {
                        repeat(i + 1, len)
                    }
                }

            };
            let tw = text.node.getContentSize().width
            if (tw > maxWidth) {
                let len: number = strArr.length;
                repeat(1, len);
            }
        }
    }
}
