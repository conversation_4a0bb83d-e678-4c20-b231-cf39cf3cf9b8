import { LogUtil } from "./LogUtil";

export class LoaderTools {

    static async loadRes<T extends cc.Asset>(bundleName: string, path: string): Promise<T> {
        return new Promise<T>(async (resolve) => {
            let bundle = !bundleName || bundleName == "resources" ? cc.resources : await LoaderTools.loadBundle(bundleName);
            if (bundle) {
                bundle.load<T>(path, (err: Error, assets: T) => {
                    if (err) {
                        LogUtil.print("loadRes Fail", bundleName, path, err);
                        resolve(null);
                    } else {
                        resolve(assets);
                    }
                })
            } else {
                LogUtil.print("loadRes Fail ->Not Find Bundle:", bundleName, path);
                resolve(null);
            }
        });
    }

    static async loadBundle(bundle: string): Promise<cc.AssetManager.Bundle> {
        return new Promise<cc.AssetManager.Bundle>((resolve) => {
            cc.assetManager.loadBundle(bundle, (err: Error, bundle: cc.AssetManager.Bundle) => {
                if (err) {
                    LogUtil.print("loadBundle Fail", bundle, err);
                    resolve(null);
                } else {
                    resolve(bundle);
                }
            })
        });
    }

    static async loadDir<T extends cc.Asset>(bundleName: string, path: string, type: typeof cc.Asset) {
        return new Promise<T[]>(async (resolve) => {
            let bundle = !bundleName || bundleName == "resources" ? cc.resources : await LoaderTools.loadBundle(bundleName);
            bundle.loadDir(path, type, (err: Error, assets: T[]) => {
                if (err) {
                    LogUtil.error("loadDir: ", path, err);
                }
                resolve(assets);
            });
        });
    }

    static async loadSkeleton(bundleName: string, path: string): Promise<sp.SkeletonData> {
        return new Promise<sp.SkeletonData>(async (resolve) => {
            let bundle = !bundleName || bundleName == "resources" ? cc.resources : await LoaderTools.loadBundle(bundleName);
            if (bundle) {
                bundle.load<sp.SkeletonData>(path, sp.SkeletonData, (err: Error, assets: sp.SkeletonData) => {
                    if (err) {
                        console.log("loadSkeleton Fail", bundleName, path, err);
                        resolve(null);
                    } else {
                        resolve(assets);
                    }
                })
            } else {
                LogUtil.print("loadSkeleton Fail ->Not Find Bundle:", bundleName, path);
                resolve(null);
            }
        });
    }

    static async loadSpriteFrame(bundleName: string, path: string): Promise<cc.SpriteFrame> {
        return new Promise<cc.SpriteFrame>(async (resolve) => {
            let bundle = !bundleName || bundleName == "resources" ? cc.resources : await LoaderTools.loadBundle(bundleName);
            if (bundle) {
                bundle.load<cc.SpriteFrame>(path, cc.SpriteFrame, (err: Error, assets: cc.SpriteFrame) => {
                    if (err) {
                        LogUtil.print("loadSpriteFrame Fail", bundleName, path, err);
                        resolve(null);
                    } else {
                        resolve(assets);
                    }
                })
            } else {
                LogUtil.print("loadSpriteFrame Fail ->Not Find Bundle:", bundleName, path);
                resolve(null);
            }
        });
    }
}
