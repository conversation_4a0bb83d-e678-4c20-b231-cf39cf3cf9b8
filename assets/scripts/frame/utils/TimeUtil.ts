export class TimeUtil {
    static isSameDay(ts1: number, ts2: number): boolean {
        let date1 = new Date(ts1);
        let date2 = new Date(ts2);
        return date1.setHours(0, 0, 0, 0) == date2.setHours(0, 0, 0, 0);
    }

    static isToday(ts: number) {
        let date1 = new Date();
        let date2 = new Date(ts);
        return date1.setHours(0, 0, 0, 0) == date2.setHours(0, 0, 0, 0);
    }
}