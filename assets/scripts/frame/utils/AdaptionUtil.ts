export class AdapationUtil {

    // 根据比例计算  适配不同屏幕尺寸下的坐标偏移
    static getCustomAdaptionRate() {
        var rate = cc.winSize.height / cc.winSize.width;
        var rate1 = 1624 / 750;
        var rate2 = 1920 / 1080;
        return (rate - rate2) / (rate1 - rate2);
    }

    static getCustomAdaption(min: number, max: number): number {
        return min + (max - min) * this.getCustomAdaptionRate();
    }
}