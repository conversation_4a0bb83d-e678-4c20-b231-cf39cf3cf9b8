import Vec3 = cc.Vec3;
export default class Utils {
    /**\
     * 获取URL参数
     */
    public static getQueryString(name: string) {
        let search = location.search;
        if (search.indexOf('?') != -1) {
            search = search.substring(1);
        }
        let urls = search.split('&');
        for (let i = 0; i < urls.length; i++) {
            if (urls[i].split('=')[0] == name) {
                return urls[i].split('=')[1];
            }
        }
        return '';
    }

    /**
     * 添加一个节点
     * @param parent 父节点 
     * @param name 节点名称
     * @param size 节点大小
     */
    public static addNode(parent: cc.Node, name?: string, size: cc.Size = cc.view.getVisibleSize()): cc.Node {
        let node = new cc.Node(name);
        node.setContentSize(size);
        parent.addChild(node);
        return node;
    }

    /**
     * 从min-max范围内随机一个正整数
     * @param min 最小值
     * @param max 最大值
     * @returns 
     */
    public static randomInt(max: number, min: number = 0) {
        let list: number[] = []
        for (let i = min; i <= max; i++) {
            list.push(i);
        }
        list.sort((a, b) => {
            return Math.random() - 0.5;
        })
        return list[0];
    }

    // [min, max)
    public static randomRange(max: number, min: number): number {
        return Math.floor(Math.random() * ((max + 1) - min) + min);
    }

    static jumpTo_v3(to: Vec3, height: number): Readonly<Vec3> {
        return { value: to, progress: this._jumpTo_progress(3, height) } as any as Vec3;
    }
    static jumpTo_v2(to: Vec3, height: number): Readonly<cc.Vec2> {
        return { value: to, progress: this._jumpTo_progress(2, height) } as any as cc.Vec2;
    }
    private static _jumpTo_progress(max: number, height: number): (from: number, to: number, cur: number, pcs: number) => number {
        let i = max;
        let heightSqrt = Math.sqrt(height);
        return (from: number, to: number, cur: number, pcs: number) => {

            // 使用序列耦合区分xyz轴: 1: x, 2: y, 3: z
            if (i >= max) i = 1;
            else i++;

            let rsl = from + (to - from) * pcs; // lerp

            if (i === 2) { // y轴的增量算法
                let du = Math.abs(1 - pcs * 2); // [0,1] > [1,0,1]
                rsl += height - Math.pow(heightSqrt * du, 2);
            }

            return rsl;
        };
    }

    /**
 * 判断对象是否为数组
 * @param obj
 * @returns
 */
    private static IsArray(obj: any) {
        return obj && typeof obj == "object" && obj instanceof Array;
    }

    /**
    * 
    * @param obj 深度拷贝对象
    */
    public static deepCopy<T>(obj: T): T {
        let str = JSON.stringify(obj);
        let newobj = JSON.parse(str);
        return newobj;
    }

    /**
     * 对象浅拷贝
     * @param tSource
     * @returns
     */
    public static SimpleClone<T>(tSource: T, tTarget?: Record<string, any> | T): T {
        if (this.IsArray(tSource)) {
            tTarget = tTarget || [];
        } else {
            tTarget = tTarget || {};
        }
        for (const key in tSource) {
            if (Object.prototype.hasOwnProperty.call(tSource, key)) {
                tTarget[key] = tSource[key];
            }
        }
        return tTarget as T;
    }

    public static async sleep(time: number) {
        return new Promise((resolve) => {
            cc.tween({})
                .delay(time)
                .call(() => {
                    resolve(null);
                })
                .start();
        });
    }

    /**
 * 图片置灰
 * 会循环遍历所有子节点，只要是图片就都置灰
 * @param sp 
 */
    public static setGray(node: cc.Node) {
        const grayMat = cc.Material.createWithBuiltin(cc.Material.BUILTIN_NAME.GRAY_SPRITE + "", 0);
        node?.getComponent(cc.Sprite)?.setMaterial(0, grayMat);
        node?.getComponent(cc.Label)?.setMaterial(0, grayMat);
        node?.children.forEach(e => {
            Utils.setGray(e);
        })
    }

    public static setNormal(node: cc.Node) {
        const normalMat = cc.Material.getBuiltinMaterial("2d-sprite");
        node?.getComponent(cc.Sprite)?.setMaterial(0, normalMat);
        node?.getComponent(cc.Label)?.setMaterial(0, normalMat);
        node?.children.forEach(e => {
            Utils.setNormal(e);
        })
    }
}