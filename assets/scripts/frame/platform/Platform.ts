import DeviceInfo from "../model/DeviceInfo";
import { SyncPromise } from "../utils/SyncPromise";
import { AdEventData, AdFormat, AdStatus, IPlatform } from "./IPlatform";

export class Platform implements IPlatform {
    /**proto初始化的参数**/
    protected proto_data: any;
    /**sdk完成回调函数**/
    protected sdk_complete: Function;
    /**proto完成回调**/
    protected protoComplete: Function;
    /**使用静态proto,否则使用动态proto**/
    protected useStaticProto: boolean = true;
    protected doneAdShowPromise: SyncPromise<AdStatus> = null;
    protected adData: AdEventData = new AdEventData();
    protected adFail: boolean = false;

    getDeviceInfo(): DeviceInfo {
        return null
    }

    //向sdk发送事件通知
    public notifySDK(eventName: string, jsonStr: string) { }

    //发送Adjust打点事件
    public sendAdjustEvent(eventName: string, jsonStr: string) {
    }

    public giveReward() {

    }

    //获得公参
    public getCommonParams() {
        let data = new Map<string, string>();
        data.set("logtime", Date.now().toString());
        return JSON.stringify(new Object());
    }

    //获得FcmToken
    public getFcmToken() {
        return "1";
    }

    //发送firebase打点事件
    public sendFirebaseEvent(eventName: string, jsonStr: string) { }

    public sendAdjustRevenue(eventName: string, revenue) { };

    startUp(): void {
    }

    loadAd(force = false) {
    }

    //加载广告
    updateAdTime(time) {
    }

    protected getMadianCode(status: AdStatus): number {
        if (status == AdStatus.LoadFailed) {
            return 2;
        }
        if (status == AdStatus.ShowFailed) {
            return 4;
        }
        if (status == AdStatus.ShowSuccess) {
            return 1;
        }
        return 5;
    }

    //播放广告
    async playAd(adFormat: AdFormat, placementName: string): Promise<AdStatus> {
        this.adData.adFormat = adFormat;
        this.adData.placementName = placementName;
        this.adData.status = AdStatus.LoadFailed;
        if (adFormat == AdFormat.REWARD || !this.doneAdShowPromise) {
            this.doneAdShowPromise = new SyncPromise<AdStatus>();
        }
        return this.doneAdShowPromise.promise;
    }

    rewardAdIsReady(placementName: string): Boolean {
        return false
    }

    initPaying() { }

    copyStr(str: string) { }
    buyProducts(ProductId: string) { }

    vibrate(duration: number, amplitude: number) { }

    vibrateCancel() { }

    rateUs() { }

    //显示banner
    showBanner(placementName: string): Boolean {

        return true;
    }

    //隐藏banner
    hideBanner(placementName: string): Boolean {

        return true;
    }

    interstitialAdIsReady(placementName: string): Boolean {
        return true;
    }

    isInStandaloneMode(): boolean {
        return true;
    }

    addOfflineNotification(json: string) {

    }

    clearOfflineNotification() {

    }

    setCustomData(json: string) {

    }
}
