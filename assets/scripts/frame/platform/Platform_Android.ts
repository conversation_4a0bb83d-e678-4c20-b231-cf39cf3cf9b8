import App from "../app/App";
import { EventName } from "../const/EventName";
import { LocalizationMgr } from "../localization/LocalizationMgr";
import DeviceInfo from "../model/DeviceInfo";
import { uiManager } from "../ui/UIManager";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { AdEventData, AdFormat, AdStatus, IPlatform } from "./IPlatform";
import { Platform } from "./Platform";
import { ADPlacementName } from "../const/ADPlacementName";
import { LogUtil } from "../utils/LogUtil";


export default class Platform_Android extends Platform implements IPlatform {
    protected proto_data: any = ["pb", "gamemsg"];
    private ApiAndroid: string = "org/cocos2dx/javascript/customSdk/sdkManager";
    private BillingAndroid: string = "org/cocos2dx/javascript/customSdk/BillingManager";
    private lastDone = null;

    getDeviceInfo(): DeviceInfo {
        let deviceInfo = new DeviceInfo();

        let data = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "JSBGetDeviceInfo", "()Ljava/lang/String;");
        console.log("data", data);
        let deviceData = JSON.parse(data);
        deviceInfo.injection(deviceData);

        let aid = LocalStorageMgr.getStringValue("aid", "");
        if (aid != "") {
            deviceInfo.aid = aid;
        }

        return deviceInfo;
    }

    startUp() {
        this.addEvent();
    }

    private addEvent() {
        App.event.addEventListener(EventName.SDK_AD_EVENT, this.onSdkEvent, this);

    }

    private removeEvent() {
        App.event.removeEventListener(EventName.SDK_AD_EVENT, this.onSdkEvent, this);
    }

    //初始化内购
    initPaying() {
        jsb.reflection.callStaticMethod(this.BillingAndroid, "initPaying", "()Ljava/lang/String;");
    }

    buyProducts(ProductId: string): number {
        let isOK = jsb.reflection.callStaticMethod(this.BillingAndroid, "BuyProducts", "(Ljava/lang/String;)I",
            ProductId);
        return isOK;
    }

    public giveReward() {
        if (this.doneAdShowPromise) {
            if (this.lastDone) {
                this.adData.status = AdStatus.RewardUser;
            }
            this.doneAdShowPromise.resolve(this.adData.status);
            this.doneAdShowPromise = null;
        }
    }

    private onSdkEvent(eventName: string, data: AdEventData) {
        LogUtil.print("eventName", JSON.stringify(data));
        if (this.doneAdShowPromise && (!data.adFormat || data.adFormat == this.adData.adFormat) && data.placementName == this.adData.placementName) {
            if (data.status > this.adData.status) {
                this.adData.status = data.status;
            }
            switch (data.status) {
                case AdStatus.LoadFailed:
                    break;
                case AdStatus.LoadSuccess:
                    this.adFail = false;
                    break;
                case AdStatus.ShowFailed:
                    this.adFail = true;
                    uiManager.tip(LocalizationMgr.ins.t("ToastWatchADFailed"));
                    if (data.adFormat == AdFormat.REWARD) {
                        this.lastDone = this.doneAdShowPromise;
                        setTimeout(() => {
                            App.event.dispatch(EventName.CHECK_REWARD_INTER);
                        }, 500);
                    } else {
                        this.doneAdShowPromise.resolve(data.status);
                        this.doneAdShowPromise = null;
                    }
                    break;
                case AdStatus.ShowSuccess:
                    App.event.dispatch(EventName.HIDE_REWARD_TOAST);
                    break;
                case AdStatus.Click:
                    break;
                case AdStatus.Close:
                    if (this.lastDone && data.placementName == ADPlacementName.InterPlacement) {
                        this.adData.status = AdStatus.RewardUser;
                    }
                    this.doneAdShowPromise.resolve(this.adData.status);
                    this.doneAdShowPromise = null;
                    this.lastDone = null;
                    //}, 300);
                    break;
                case AdStatus.RewardUser:
                    break;
                case AdStatus.RevenuePaid:
                    break;
                default:
                    break;
            }
        }
    }

    //加载广告
    loadAd(force = false) {
        LogUtil.print("JSB loadAd");
        jsb.reflection.callStaticMethod(this.ApiAndroid, "ForceLoadAds", "()Ljava/lang/String;");
    }

    //加载广告
    updateAdTime(time) {
        jsb.reflection.callStaticMethod(this.ApiAndroid, "updateAdTime", "(Ljava/lang/String;)V", time.toString());
    }

    //播放广告
    async playAd(adFormat: AdFormat, placementName: string): Promise<AdStatus> {

        let adPro = super.playAd(adFormat, placementName);
        if (adFormat == AdFormat.REWARD) {
            LogUtil.print("ad==>", "playRewardAd " + placementName);
            // uiManager.open(UIID_MAIN.RewardToastPop);   // TODO:

            setTimeout(() => {
                let isOK = this.playRewardAd(placementName);
                if (!isOK) {
                    let adData = new AdEventData();
                    adData.adFormat = adFormat;
                    adData.placementName = placementName;
                    adData.status = AdStatus.LoadFailed;
                    App.event.dispatch(EventName.SDK_AD_EVENT, adData);
                }
            }, 300);
        }
        else if (adFormat == AdFormat.INTERSTITIAL) {
            console.log("ad==>", "playInterstitialAd " + placementName);
            let isOK = jsb.reflection.callStaticMethod(this.ApiAndroid, "JSBPlayInterstitialAd", "(Ljava/lang/String;)Z",
                placementName);
            if (!isOK) {
                let adData = new AdEventData();
                adData.adFormat = adFormat;
                adData.placementName = placementName;
                adData.status = AdStatus.LoadFailed;
                App.event.dispatch(EventName.SDK_AD_EVENT, adData);
            }
        }
        return adPro;
    }

    public playRewardAd(placement: string, v: string = "0") {
        return jsb.reflection.callStaticMethod(this.ApiAndroid, "JSBPlayRewardAd", "(Ljava/lang/String;Ljava/lang/String;)Z", placement, v);
    }

    //向sdk发送时间通知
    public notifySDK(eventName: string, jsonStr: string) {
        jsb.reflection.callStaticMethod(this.ApiAndroid, "JSBNotify", "(Ljava/lang/String;Ljava/lang/String;)V",
            eventName, jsonStr);
    }

    //发送Adjust打点事件
    public sendAdjustEvent(eventName: string, jsonStr: string) {
        jsb.reflection.callStaticMethod(this.ApiAndroid, "sendAdjustEvent", "(Ljava/lang/String;Ljava/lang/String;)V",
            eventName, jsonStr);
    }

    //发送firebase打点事件
    public sendFireBaseEvent(eventName: string, jsonStr: string) {
        jsb.reflection.callStaticMethod(this.ApiAndroid, "sendFirebaseEvent", "(Ljava/lang/String;Ljava/lang/String;)V",
            eventName, jsonStr);
    }

    //发送AdjustSetRevue打点事件
    public sendAdjustRevenue(eventName: string, productId) {
        jsb.reflection.callStaticMethod(this.BillingAndroid, "sendAdjustRevenue", "(Ljava/lang/String;Ljava/lang/String;)V",
            eventName, productId);
    }

    //显示banner
    showBanner(placementName: string): Boolean {
        LogUtil.print("JSB showBanner");
        let isOK = jsb.reflection.callStaticMethod(this.ApiAndroid,
            "showBanner", "(Ljava/lang/String;)Z",
            placementName);

        return isOK;
    }

    //隐藏banner
    hideBanner(placementName: string): Boolean {
        LogUtil.print("JSB hideBanner");
        let isOK = jsb.reflection.callStaticMethod(this.ApiAndroid,
            "hideBanner", "(Ljava/lang/String;)Z",
            placementName);

        return isOK;
    }

    //检查广告是否加载
    rewardAdIsReady(placementName: string): Boolean {
        let isOK = jsb.reflection.callStaticMethod(this.ApiAndroid, "JSBPlayRewardAdIsReady", "(Ljava/lang/String;)Z",
            placementName);
        LogUtil.print(`rewardAdIsReady ${placementName} ${isOK}`);
        return isOK;
    }

    interstitialAdIsReady(placementName: string): boolean {
        let isOK = jsb.reflection.callStaticMethod(this.ApiAndroid, "JSBInterstitialAdIsReady", "(Ljava/lang/String;)Z",
            placementName);
        return isOK;
    }


    //获得公参
    public getCommonParams() {
        let str = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "JSBGetCommonParams", "()Ljava/lang/String;");
        return str;
    }

    //获得FcmToken
    public getFcmToken() {
        let str = jsb.reflection.callStaticMethod(this.ApiAndroid, "getFcmToken", "()Ljava/lang/String;");
        return str;
    }


    copyStr(str: string): void {
        jsb.reflection.callStaticMethod(this.ApiAndroid, "copyStr", "(Ljava/lang/String;)V",
            str);
    }


    vibrate(duration: number, amplitude: number) {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "vibrate", "(II)V", duration, amplitude);
    }
    vibrateCancel() {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "cancelVibrate", "()V");
    }

    rateUs() {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "rateUs", "()V");
    }

    addOfflineNotification(json: string): void {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "addOfflineNotification", "(Ljava/lang/String;)V", json);
    }

    clearOfflineNotification(): void {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "clearOfflineNotification", "()V");
    }

    setCustomData(json: string): void {
        jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "setCustomData", "(Ljava/lang/String;)V", json);
    }
}
