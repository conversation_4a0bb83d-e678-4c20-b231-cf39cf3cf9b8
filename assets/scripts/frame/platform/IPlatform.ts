import DeviceInfo from "../model/DeviceInfo";

export enum AdFormat {
    REWARD = 0,                 //激励视频
    INTERSTITIAL = 1            //插屏
}

export enum AdStatus {
    LoadFailed = 0,             //加载失败
    LoadSuccess = 1,            //加载成功
    ShowFailed = 2,             //显示失败
    ShowSuccess = 3,            //显示成功
    Click = 4,                  //点击广告
    Close = 5,                  //关闭广告
    RewardUser = 6,             //奖励用户
    RevenuePaid = 7,            //广告收入回调
}

export class AdEventData {
    adFormat: AdFormat = 0;
    placementName: string = "";
    status: AdStatus = 0;
}

export interface IPlatform {
    getDeviceInfo(): DeviceInfo;
    /**向sdk发送事件通知 */
    notifySDK(eventName: string, jsonStr: string);
    /**发送Adjust打点事件 */
    sendAdjustEvent(eventName: string, jsonStr: string);
    /**发送firebase打点事件 */
    sendFirebaseEvent(eventName: string, jsonStr: string);
    /**发送Adjust打点事件 */
    sendAdjustRevenue(eventName: string, productId);
    /**播放广告 */
    startUp();
    giveReward();
    loadAd(force: boolean);
    rewardAdIsReady(placementName: string): Boolean;
    interstitialAdIsReady(placementName: string): Boolean;
    playAd(adFormat: AdFormat, placementName: string): Promise<AdStatus>;
    showBanner(placementName: string): Boolean;
    hideBanner(placementName: string): Boolean;
    initPaying();
    copyStr(str: string);
    buyProducts(ProductId: string);
    vibrate(duration: number, amplitude: number);
    vibrateCancel();
    rateUs();
    //加载广告
    updateAdTime(time);
    /**获取打点公参 */
    getCommonParams();
    getFcmToken();

    isInStandaloneMode(): boolean;

    addOfflineNotification(json: string);
    clearOfflineNotification();
    setCustomData(json: string);
}