import App from "../app/App";
import DeviceInfo from "../model/DeviceInfo";
import { LocalStorageMgr } from "../utils/LocalStorageMgr";
import { TimeUtil } from "../utils/TimeUtil";
import { AdFormat, AdStatus, IPlatform } from "./IPlatform";
import { Platform } from "./Platform";
import FingerprintJS from '@fingerprintjs/fingerprintjs';

export default class Platform_Web extends Platform implements IPlatform {
    protected lan: string = "zh_CN";
    protected proto_data: any = ["pb", "gamemsg"];


    deviceInfo = new DeviceInfo();
    getDeviceInfo() {
        // com.screw.nut.wood.puzzle
        let mydev = {
            "pkg": "com.debug.wood.puzzle",
            "appver": "1",
            "appver_name": "1.0.21",
            "channel": "Organic",
            "pf": "web",
            "total_memory": "4097"
        };
        this.deviceInfo.injection(mydev);
        let aid =  this.getAid();
        this.deviceInfo.aid = aid;
        this.deviceInfo.did = aid;
        this.deviceInfo.timezone = this.getTimezone();
        this.deviceInfo.country = this.convertCountry(navigator.language)
        this.deviceInfo.language = navigator.language;
        this.deviceInfo.mod = navigator.vendor;
        this.deviceInfo.install_info = navigator.userAgent;
        this.deviceInfo.bucket = this.getBucket().toString()
        return this.deviceInfo;
    }

    public getCommonParams() {
        let data = new Map<string, string>();
        data["uid"] = this.deviceInfo.aid;
        data["anm"] = App.info.anm;
        data["subanm"] = App.info.subanm;
        data["pkg"] = this.deviceInfo.pkg;
        data["verc"] = this.deviceInfo.appver;
        data["ver"] =  this.deviceInfo.appver_name;
        data["pf"] =  this.deviceInfo.pf;
        data["did"] =  this.deviceInfo.did;
        data["gaid"] =  this.deviceInfo.did;
        data["aid"] =  this.deviceInfo.aid;
        data["brd"] =  this.deviceInfo.brd;
        data["mod"] =  this.deviceInfo.mod;
        data["cou"] =  this.deviceInfo.country;
        data["slan"] =  this.deviceInfo.language;
        data["sid"] = this.getSoftwareId();
        data["bucket"] = this.getBucket().toString();
        data["os"] = navigator.platform;
        data["net"] = navigator.onLine ? "online" : "no_net";
        data["sys_vc"] = navigator.appVersion;
        data["sys_vn"] = navigator.appCodeName;
        data["logtime"] = Date.now().toString();
        data["log_id"] = crypto.randomUUID();
        return JSON.stringify(data)
    }


    convertCountry(lan: string) {
        let arr = lan.split("-");
        return arr[1] ? arr[1] : "IN";
    }

    getTimezone(): number {
        return - new Date().getTimezoneOffset() / 60;
    }

    isInStandaloneMode(): boolean {
        return window.matchMedia('(display-mode: standalone)').matches || document.referrer.includes('android-app://')
    }

    public giveReward() {
        console.log(this.doneAdShowPromise)
        if (this.doneAdShowPromise) {
            this.adData.status = AdStatus.RewardUser;

            this.doneAdShowPromise.resolve(this.adData.status);
            this.doneAdShowPromise = null;
        }
    }

     getAid() {
        let aid = LocalStorageMgr.getStringValue("aid", "");
        if (aid != "") return aid;
        aid = (Date.now() / 1000).toString();
        LocalStorageMgr.setStringValue("aid", aid);
        return aid;
    }

    getBucket(): number {
        let bucket = LocalStorageMgr.getValue("bucket", -1);
        if (bucket > 0) return bucket;

        bucket = Math.abs(this.hashCode(this.getSoftwareId()) % 100);
        LocalStorageMgr.setValue("bucket", bucket);
        return bucket;
    }

    getSoftwareId(): string {
        let sid = LocalStorageMgr.getStringValue("sid", "");
        if (sid != "") return sid;

        sid = crypto.randomUUID();
        LocalStorageMgr.setStringValue("sid", sid);
        return sid;
    }

    hashCode(string: string): number {
        let hash: number = 0;
        const { length }: string = string;
        for (let i: number = 0; i < length; i++) {
            const code: number = string.codePointAt(i) as number;
            hash = (hash << 5) - hash + code;
            hash |= 0; // Convert to 32bit integer
        }
        return hash;
    }

    rewardAdIsReady(placementName: string): Boolean {
        return true;
    }

    async playAd(adFormat: AdFormat, placementName: string): Promise<AdStatus> {
        return AdStatus.RewardUser;
    }
}