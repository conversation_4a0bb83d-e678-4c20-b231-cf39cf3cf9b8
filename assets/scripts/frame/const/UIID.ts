import { UIConf } from "../ui/UIManager";

export enum UIID {
    UILoading = 200,
    UILoadComponent,
    UINetError,
    UITip,
}

export let UICF: { [key: number]: UIConf } = {
    [UIID.UILoading]: { prefab: "loading/UILoading", bundle: "resources" },
    [UIID.UILoadComponent]: { prefab: "prefab/LoadComponent", bundle: "common" },
    [UIID.UITip]: { prefab: "prefab/TipNode", bundle: "common" },
    [UIID.UINetError]: { prefab: "prefab/UINetError", bundle: "common" },
}