import App from "./frame/app/App";
import { AppInfo } from "./frame/app/AppInfo";
import { LogMgr } from "./frame/app/LogMgr";
import { EventName } from "./frame/const/EventName";
import { UICF, UIID } from "./frame/const/UIID";
import { EventMgr } from "./frame/event/EventManager";
import { audioManager } from "./frame/manager/audioManager";
import { uiManager } from "./frame/ui/UIManager";
import { LocalStorageMgr } from "./frame/utils/LocalStorageMgr";
import { CONFIGS, LANGUAGE_CONFIG, UICF_MAIN } from "./GameConst";
import { ConfigManager } from "./managers/ConfigManager";
import GameMgr from "./managers/GameMgr";
import { TrackLogMgr } from "./managers/TrackLogMgr";
import { ModelMgr } from "./model/ModelMgr";
import UILoading from "./view/UILoading";
const { ccclass } = cc._decorator;

@ccclass
export default class Main extends cc.Component {
    private hideTimeSt;

    protected onLoad(): void {
        var canvas = cc.find("Canvas").getComponent(cc.Canvas);
        if (cc.winSize.height / cc.winSize.width < 1.77) {
            canvas.fitHeight = true;
        } else {
            canvas.fitWidth = true;
        }
    }

    async start() {
        let startTime = Date.now();
        cc.debug.setDisplayStats(false);
        //@ts-ignore
        if (window.vConsole) {
            //@ts-ignore
            window.vConsole.destroy();
            //@ts-ignore
            window.vConsole = null;
        }
        App.initPlatform(new AppInfo());
        ModelMgr.ins.init();

        const LoginCount = LocalStorageMgr.getValue("Wood_LoginCount", 0);
        if (LoginCount == 0)
            TrackLogMgr.addLog("app_install");

        LocalStorageMgr.setValue("Wood_LoginCount", LoginCount + 1);

        TrackLogMgr.addLog("app_start", { start_type: 1 });

        let currentTime = Date.now();

        uiManager.initialize(UICF, UICF_MAIN);
        await uiManager.open(UIID.UILoading)
        let uiLoading = uiManager.getUI(UIID.UILoading) as UILoading;
        uiLoading.updateProgress(0.4, 3);

        this.hideTimeSt = Date.now();
        this.addEvent();
        this.addGlobalBtnClick();

        await App.initialize(LANGUAGE_CONFIG);
        await ConfigManager.ins.initConfigs(CONFIGS)

        TrackLogMgr.addLog("initialization_config", { duration: Math.floor(Date.now() - currentTime) });
        this.enterReq();
        uiLoading.updateProgress(0.8, 3);

        await GameMgr.ins.preloadAssets();

        currentTime = Date.now();
        uiLoading.updateProgress(1, 1 - uiLoading.progressbar.progress, () => {
            GameMgr.ins.startGame();
            audioManager.instance.playMusic("bgm");
        });

        TrackLogMgr.addLog("initialization_loading_resource", {
            duration: Math.floor(Date.now() - currentTime),
            loading_duration: Math.floor(Date.now() - startTime),
        });

        cc.game.on(cc.game.EVENT_SHOW, this.onGameShow, this);
        cc.game.on(cc.game.EVENT_HIDE, this.onGameHide, this);
        this.schedule(this.checkLog, 60, cc.macro.REPEAT_FOREVER);
    }

    private addEvent() {


    }

    private removeEvent() {
        cc.game.off(cc.game.EVENT_SHOW, this.onGameShow, this);
        cc.game.off(cc.game.EVENT_HIDE, this.onGameHide, this);
    }

    private onGameHide() {
        this.hideTimeSt = new Date().getTime();
        let addata = { end_type: 2, duration: Math.floor(ModelMgr.ins.common.activityTime) }
        TrackLogMgr.addLog("app_end", addata);
        ModelMgr.ins.common.activityTime = 0;
    }

    private onGameShow() {
        let curTime = new Date().getTime();
        let dt = curTime - this.hideTimeSt;
        if (dt > 1000) {
            TrackLogMgr.addLog("app_start", { start_type: 2 });
        }
    }

    private async enterReq() {
        TrackLogMgr.addLog("login");
        let current_time = new Date().getTime();

        let mdata = {
            duration: new Date().getTime() - current_time,
        }
        TrackLogMgr.addLog("login_success", mdata);
    }


    private checkLog() {
        LogMgr.checkLog();
    }


    private _hideNativeSplash() {
        if (CC_JSB) {
            if (cc.sys.os == cc.sys.OS_ANDROID) {
                // 反射调用原生的隐藏方法
                jsb.reflection.callStaticMethod(
                    "org/cocos2dx/javascript/AppActivity",
                    "hideSplash",
                    "()V"
                );
            }
        }
    }

    protected update(dt: number): void {
        ModelMgr.ins.common.addActivityTime(dt);
    }

    protected onDestroy(): void {
        this.removeEvent();
    }

    private addGlobalBtnClick() {
        let Super = function () { };
        Super.prototype = cc.Button.prototype;
        //实例化原型
        Super.prototype._onTouchEnded = function (t) {
            if (this.interactable && this.enabledInHierarchy) {
                audioManager.instance.playSound("click");
                audioManager.instance.vibrate(30, 160);
                if (this._pressed) {
                    cc.Component.EventHandler.emitEvents(this.clickEvents, t);
                    this.node.emit('click', this);
                }
                this._pressed = !1;
                this._updateState();
                t.stopPropagation();
                EventMgr.dispatch(EventName.ON_CLICK_BTN);
            }
        };
    }

}
