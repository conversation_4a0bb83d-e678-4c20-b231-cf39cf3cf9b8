
export const PinColor = cc.Enum({
    White: 0,
    <PERSON><PERSON><PERSON>w: 1,
    Red: 2,
    Orange: 3,
    Yellow: 4,
    Green: 5,
    <PERSON><PERSON>: 6,
    Blue: 7,
    Purple: 8,
    <PERSON>: 9,
});

export class PinItemConfig{
    x: number;
    y: number;
    width: number;
    height: number;
}

export class BlockItemConfig{
    x: number;
    y: number;
    width: number;
    height: number;
    pins: PinItemConfig[];
    type: number;
    shape: number;
    rotation: number;
}

export class LayerConfig{
    x: number;
    y: number;
    idx: number;
    blocks: BlockItemConfig[];
}

export class LevelConfig{
    level: number;
    layers: LayerConfig[];
    poleCount: number;
    tsaCount: number;
}