import { LocalStorageMgr } from "../frame/utils/LocalStorageMgr";

export class CommonModel {
    
    private _activityTime: number = 0;    //游戏活跃时间
    public _saveDt: number = 0;

    constructor() {
        this._activityTime = LocalStorageMgr.getValue("ActivityTime", 0);
    }

    get activityTime() {
        return Math.ceil(this._activityTime);
    }
    set activityTime(time: number) {
        this._activityTime = time;
        LocalStorageMgr.setValue("ActivityTime", this._activityTime);
    }

    public addActivityTime(dt: number) {
        this._saveDt += dt * 1000;
        this._activityTime += dt * 1000;
        if (this._saveDt > 3000) {
            LocalStorageMgr.setValue("ActivityTime", this._activityTime);
            this._saveDt = 0;
        }
    }
}