import App from "../frame/app/App";
import { CommonModel } from "./CommonModel";
import { GameModel } from "./GameModel";

export class ModelMgr {
    private static _ins: ModelMgr;
    public static get ins(): ModelMgr { return this._ins = this._ins || new ModelMgr() }
    public game: GameModel = new GameModel();
    public common: CommonModel = new CommonModel();

    public init() {

    }

    public get token() {
        return App.device.gameToken;
    }

    public get uid(): string {
        return App.device.aid;
    }
}