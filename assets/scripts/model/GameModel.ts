import TextureFill from "../colorfill/TextureFill";
import { LocalStorageMgr } from "../frame/utils/LocalStorageMgr";
import { RandomUtil } from "../frame/utils/RandomUtil";
import BlockItem from "../game/BlockItem";
import PinItem from "../game/PinItem";
import { ConfigManager } from "../managers/ConfigManager";
import GameMgr from "../managers/GameMgr";
import { LevelConfigItem } from "./config/GuideConfig";
import { LevelConfig } from "./LevelConfig";
import { ModelMgr } from "./ModelMgr";

export class GameModel {
    readonly MAX_LEVEL = 6;

    level: number = 1;

    currentLayer: number = 1;
    blockItems: Array<cc.Node> = [];
    textureFill: TextureFill = null;
    levelDifConf: LevelConfigItem = null;
    layerUsedColors: Array<cc.Color> = [];
    tsa: Array<number> = [0, 0, 0, 0, 0]; // 用来临时存放颜色的容器
    totalColors: Array<number> = []; // 用来存放所有颜色的容器
    totalColorsCount: Array<number> = []; // 用来存放每个颜色的数量，对应着上面的totalColors，用来辅助设置pin的颜色的
    totalColorHasUsed: Array<boolean> = []; // 用来标记颜色是否已经被使用过，用来防止重复使用
    curPinColorIndex: number = 0; // 当前pin的颜色设置到哪的一个索引
    poleCount: number = 2; // 当前的杆子的数量
    tsaSize: number = 5; // 临时存放颜色的容器的大小
    compareColors: Array<number> = [];
    cmpColorCount: Array<number> = [];
    totalPinCount: number = 0;
    levelBlocks: Map<number, Array<cc.Node>> = new Map(); // 根据layer来存放block的对象
    percentage: number = 0; // 当前游戏的进度
    collectColorCount: number = 0; // 收集到的颜色数量
    levelConfig: LevelConfig = null;
    curLayerCount: number = 0;
    invisibleTsa: Array<number> = []; // 用来存放使用clean道具之后的颜色，还有hammer道具之后的颜色
    hammer = false;
    reviveCount = 0;

    enableHammer() {
        GameData.hammer = true;
    }
    disablehammer() {
        GameData.hammer = false;
    }


    constructor() {
        this.level = LocalStorageMgr.getValue("colorsort_level", 1);
    }

    passLevel() {
        this.SaveLevelProgress(1); // 保存游戏进度
        if (this.level < this.MAX_LEVEL) {
            this.level += 1;
            LocalStorageMgr.setValue("colorsort_level", this.level);
        }
    }
    SaveLevelProgress(progress : number) {
        var level_p = LocalStorageMgr.getValue("Level_" + this.level + "_Precent", 0);
        if(level_p < progress)
            LocalStorageMgr.setValue("Level_" + this.level + "_Precent", progress); // 保存游戏进度
    }

    reset() {
        this.blockItems.forEach(node => node.destroy());
        this.levelBlocks.clear();
        this.tsa = [0, 0, 0, 0, 0];
        this.totalColors = [];      // 存放所有的颜色
        this.totalColorsCount = []; // 存放每个颜色的数量，用来辅助设置pin的颜色的数组
        this.curPinColorIndex = 0; // 这个索引用来设置pin的颜色，一个颜色对应着三个pin
        this.compareColors = [];  // 用来存放比较区的颜色
        this.cmpColorCount = [];  // 用来计数的数组，对应着compareColors
        this.totalPinCount = 0; // 当前关卡的所有pin个数
        this.currentLayer = 1; // 当前显示的最大层的后面一层
        this.percentage = 0;
        this.totalColorHasUsed = []; // 用来标记颜色是否已经被使用过，用来防止重复使用
        this.collectColorCount = 0; // 收集的颜色的个数，用来计算游戏进度的
        this.levelConfig = null;
        this.curLayerCount = 0;
        this.invisibleTsa = [];
        this.hammer = false;
        this.reviveCount = 0;

        ConfigManager.ins.LevelConfig.forEach((item) => {
            if (item.Level_ID == ModelMgr.ins.game.level) {
                this.levelDifConf = item;
            }
        });
        this.layerUsedColors = [];
    }

    getFirstBlock() {
        let block: cc.Node = null;
        for (let i = 1; i < this.levelBlocks.size; i++) {
            let blocks = this.levelBlocks.get(i);
            blocks.forEach((node) => {
                if (block != null) return;
                if (node.active) {
                    block = node;
                }
            });
        }
        return block;
    }

    getColorByPercentage() {
        // for (let i = 0; i < this.totalColorHasUsed.length; i++) {
        //     console.log(i + " color: " + this.totalColors[i] + " totalColorHasUsed: " + this.totalColorHasUsed[i]);
        // }
        console.log("current game progress: " + this.percentage);

        console.log("current level difficulty: ");
        console.log("percentage 0 - 30 difficulty:" + this.levelDifConf.Progress1);
        console.log("percentage 30 - 60 difficulty:" + this.levelDifConf.Progress2);
        console.log("percentage 60 - 80 difficulty:" + this.levelDifConf.Progress3);
        console.log("percentage 80 - 100 difficulty:" + this.levelDifConf.Progress4);
        // 根据当前的关卡进度来配置难度
        //  ⅰ. 难度0: 在备用槽位+ 当前所有无遮挡的螺钉中选出螺钉数量≥3个的颜色
        // ⅱ. 难度1: 从槽位+前2层木板中选出数量≥3的螺钉颜色，并排除难度0的颜色。
        // ⅲ. 难度2: 从槽位+前3层木板中选出数量≥3的螺钉颜色，并排除难度0、难度1的颜色。
        // ⅳ. 难度3: 从槽位+前4层木板中选出数量≥1的螺钉颜色，并排除难度0、难度1、难度2的颜色。
        // ⅴ. 兜底难度策略：当配置为难度3，本次刷出时难度3无解，则难度向下取结果，即找难度2的结果，以此类推。当到难度0还出现无解时，则直接兜底选中当前无遮挡螺钉中数量最多的颜色。
        let stageColors = new Array<Map<number, number>>(4); // 存放各个阶段的颜色和数量,0表示的是noCover的颜色，1表示的是stage1的颜色，2表示的是stage2的颜色，3表示的是stage3的颜色
        let displayLayers: Array<Array<cc.Node>> = [];
        for (let i = 0; i < stageColors.length; i++) {
            stageColors[i] = new Map<number, number>();
        }
        // 先将tsa中的颜色压入这些容器中
        for (let i = 0; i < this.tsaSize; i++) {
            if (this.tsa[i] == 0) continue;
            for (let j = 0; j < stageColors.length; j++) {
                if (stageColors[j].has(this.tsa[i])) {
                    stageColors[j].set(this.tsa[i], stageColors[j].get(this.tsa[i]) + 1);
                }
                else {
                    stageColors[j].set(this.tsa[i], 1);
                }
            }
        }

        // 获取前面几层，最多能有四层
        for (let i = 1; i <= this.levelBlocks.size; i++) {
            let blocks = this.levelBlocks.get(i);
            let visible = false;
            blocks.forEach((block) => {
                block.children.forEach((pin) => {
                    if (pin.getComponent(PinItem) == null || !pin.active) return;
                    visible = true;
                });
                if (visible) return;
            })
            if (!visible) continue;
            console.log("layer: " + i + " blocks: " + blocks.length);
            displayLayers.push(blocks);
            if (displayLayers.length >= GameMgr.ins.displayLayerCount) break;
        }

        // 收集颜色个数
        for (let i = 0; i < displayLayers.length; i++) {
            displayLayers[i].forEach((block) => {
                if (block == null || !block.active) return;
                block.children.forEach((pin) => {
                    if (pin.getComponent(PinItem) == null) return;
                    if (pin.getComponent(PinItem).pinColor == 0 || !pin.active) return;
                    GameMgr.ins.collectNoCoverPin(stageColors[0], pin);
                    let curColor = pin.getComponent(PinItem).pinColor;
                    // 0表示noCover颜色，1表示难度1的颜色，2表示难度2的颜色，3表示难度3的颜色
                    // 那么每一个pin都需要判断一下是否noCover，然后存到0的位置，难度1只需要收集前两层的位置，难度2需要收集前三层的位置，难度3需要收集前四层的位置
                    // j=0 不用收集颜色的数量
                    // j=1 1,2,3会收集第1层的颜色的数量
                    // j=2 2,3会收集第2层的颜色的数量
                    // j=3 3会收集第3层的颜色的数量

                    // i=0, j=0-3
                    // i=1, j=1-3
                    // i=2, j=2-3
                    // i=3, j=3

                    for (let j = i; j < stageColors.length; j++) {
                        if (j == 0) continue;
                        if (stageColors[j].has(curColor)) {
                            stageColors[j].set(curColor, stageColors[j].get(curColor) + 1);
                        }
                        else {
                            stageColors[j].set(curColor, 1);
                        }
                    }
                });
            });
        }

        // stageColors.forEach((colors, index) => {
        //     console.log("stage" + index + " colors: ");
        //     colors.forEach((count, color) => {
        //         console.log(color + ": " + count);
        //     });
        // });
        // console.log("no deal color and count: ^^^^^^^^^^^")

        // 减去compareColor中的颜色个数
        for (let i = 0; i < this.poleCount; i++) {
            if (this.compareColors[i] == 0) continue;

            for (let j = 0; j < stageColors.length; j++) {
                if (stageColors[j].has(this.compareColors[i])) {
                    stageColors[j].set(this.compareColors[i], stageColors[j].get(this.compareColors[i]) - this.cmpColorCount[i]);
                }
            }
        }

        // 找到无遮挡的个数最多的
        let maxCount = 0; let maxColor = 0;
        stageColors[0].forEach((count, color) => {
            if (count > maxCount && this.compareColors.indexOf(color) == -1 && this.checkColorAvaiable(color)) {
                maxCount = count;
                maxColor = color;
            }
        });
        stageColors.forEach((colors, index) => {
            console.log("stage" + index + " colors: ");
            colors.forEach((count, color) => {
                console.log(color + ": " + count);
            });
        });
        // 排除前面阶段的颜色
        let retColors = new Array<Array<number>>(4);
        for (let i = 0; i < stageColors.length; i++) {
            retColors[i] = [];
            stageColors[i].forEach((count, color) => {
                let idx = this.compareColors.indexOf(color);
                let cnt = this.cmpColorCount[idx];
                if ((count >= 3 /*|| i == 0*/) && (idx == -1 || cnt == 0) && this.checkColorAvaiable(color)) {
                    let hasContained = false;
                    for (let j = 0; j < i; j++) {
                        if (retColors[j].indexOf(color) != -1) {
                            hasContained = true;
                            break;
                        }
                    }
                    if (!hasContained && this.checkColorAvaiable(color))
                        retColors[i].push(color);
                }
            });
        }
        // 至少找到一个颜色才能交差
        if (maxColor == 0) {
            for (let i = 0; i < this.totalColors.length; i++) {
                if (this.totalColorHasUsed[i]) continue;
                maxColor = this.totalColors[i];
            }
        }

        for (let i = 0; i < retColors.length; i++) {
            console.log("stage" + (i + 1) + " colors: " + retColors[i].toString());
        }
        console.log("=========================================== push color once ===========================================");
        if (this.percentage >= 0.8) { // 难度3
            for (let i = this.levelDifConf.Progress4; i >= 0; i--) {
                if (retColors[i].length > 0) return retColors[i][RandomUtil.randomRange(0, retColors[i].length)];
            }
            return maxColor;
        }
        if (this.percentage >= 0.6) { // 难度2
            for (let i = this.levelDifConf.Progress3; i >= 0; i--) {
                if (retColors[i].length > 0) return retColors[i][RandomUtil.randomRange(0, retColors[i].length)];
            }
            return maxColor;
        }
        if (this.percentage >= 0.3) { // 难度1
            for (let i = this.levelDifConf.Progress2; i >= 0; i--) {
                if (retColors[i].length > 0) return retColors[i][RandomUtil.randomRange(0, retColors[i].length)];
            }
            return maxColor;
        }
        if (this.percentage >= 0) { // 难度0
            for (let i = this.levelDifConf.Progress1; i >= 0; i--) {
                if (retColors[i].length > 0) return retColors[i][RandomUtil.randomRange(0, retColors[i].length)];
            }
            return maxColor;
        }
        return maxColor;
    }

    setTotalColors() {
        if (this.totalPinCount % 3 != 0) throw new Error("the pin count must be deviced by 3");
        this.textureFill.config.shapes.forEach((shape, idx) => { this.totalColors.push(shape.color); console.log(idx + " color: " + shape.color); });
        console.log("total color ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^");
        for (let i = 0; i < this.totalPinCount / 3; i++) {
            this.totalColorsCount.push(3);
            this.totalColorHasUsed.push(false);
        }
    }
    shuffle<T>(array: T[]) {
        for (let i = 0; i < 3; i++) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
        }
    }

    checkColorAvaiable(color) {
        for (let i = 0; i < GameData.totalColorHasUsed.length; i++) {
            if (GameData.totalColors[i] == color && !GameData.totalColorHasUsed[i]) {
                return true;
            }
        }
        return false;
    }

    totalColorEmpty() {
        let empty = true;
        for (let i = 0; i < this.totalColors.length; i++) {
            if (this.totalColorHasUsed[i]) continue;
            empty = false;
            break;
        }
        return empty;
    }

    getColorByTsa(): number {
        let retColor = 0;
        let maxCount = 0;
        let tsaColorCount = new Map<number, number>();
        this.tsa.forEach((color) => {
            if (tsaColorCount.has(color)) {
                tsaColorCount.set(color, tsaColorCount.get(color) + 1);
            }
            else {
                tsaColorCount.set(color, 1);
            }
        });
        tsaColorCount.forEach((count, color) => {
            if (count > maxCount) {
                retColor = color;
                maxCount = count;
            }
        });
        if (retColor == 0) {
            retColor = this.getColorByPercentage();
        }
        return retColor;
    }

    colorAvilable(color: number) {
        let index = -1;
        for (let j = 0; j < this.totalColors.length; j++) {
            if (this.totalColorHasUsed[j]) continue;
            if (this.totalColors[j] != color) continue;
            index = j;
            this.totalColorHasUsed[j] = true;
            break;
        }
        return index != -1;
    }

    getBlockColorByRandom() {
        let ls = [];
        for (let i = 0; i < 7; i++) {
            let col = this.colorList[i];
            if (!this.layerUsedColors.includes(col)) {
                ls.push(col);
            }
        }
        let col = ls.length > 0 ? ls[RandomUtil.randomRange(0, ls.length)] : this.colorList[0];

        this.layerUsedColors.push(col);

        return col;
    }

    readonly colorList = [
        new cc.Color().fromHEX("#b6ebff"),
        new cc.Color().fromHEX("#cdb9ff"),
        new cc.Color().fromHEX("#ffc0db"),
        new cc.Color().fromHEX("#ffd778"),
        new cc.Color().fromHEX("#ffb0a9"),
        new cc.Color().fromHEX("#aaeab9"),
        new cc.Color().fromHEX("#f4f579"),
    ]

    hasCovered(pin: cc.Node) {
        let hasCover = false;
        if (!pin.active || !pin.getComponent(cc.Sprite).enabled) return false;
        GameData.levelBlocks.forEach((blocks, layer) => {
            if (layer >= pin.getComponent(PinItem).realLayer || hasCover) return false; // 当前层比pin的层大，那么就不用判断
            blocks.forEach((block) => {
                if (!block.active || hasCover || block === pin.parent) return false;
                let topLocalPos = block.convertToNodeSpaceAR(pin.convertToWorldSpaceAR(cc.v2(0, 25)));
                if (block.getComponent(BlockItem).isCirclePolygon(topLocalPos, 30)) {
                    hasCover = true;
                }
            });
        });
        return hasCover;
    }

    init() {
        GameData.poleCount = GameData.levelConfig.poleCount;
        GameData.tsaSize = GameData.levelConfig.tsaCount;
        GameData.setTotalColors(); // 这里是固定队列的颜色
    }

    updateProgress() {
        this.percentage = this.collectColorCount / this.totalColors.length;
    }

    sortBlock(idx: number) {
        GameData.levelBlocks.get(idx).sort((a, b) => b.y - a.y);
        GameData.levelBlocks.get(idx).forEach((block, index) => {
            block.setSiblingIndex(index);
        });
    }

    showNextLayerOutline() {
        GameData.levelBlocks.forEach((blocks, layer) => {
            if (layer != GameData.currentLayer) return;
            blocks.forEach((block) => {
                block.active = true;
                block.opacity = 1;
                block.children.forEach((pin) => {
                    pin.active = false;
                });
            });
        });
    }

    updateCmpData(idx: number, color: number) {
        this.compareColors[idx] = color;
        this.cmpColorCount[idx] = 3;
        this.collectColorCount++;
        this.updateProgress();
    }
}

export const GameData = ModelMgr.ins.game;