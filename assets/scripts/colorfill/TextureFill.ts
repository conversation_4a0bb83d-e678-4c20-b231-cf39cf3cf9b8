import { LoaderTools } from "../frame/utils/LoaderTools";
import Utils from "../frame/utils/Utils";
import { ConfigManager } from "../managers/ConfigManager";
import GameMgr from "../managers/GameMgr";
import { TexFillConfigItem } from "../model/config/TexFillConfig";
import RenderUtil from "./RenderUtil";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TextureFill extends cc.Component {
    @property(cc.Node) origin: cc.Node = null;
    @property(cc.Node) shape: cc.Node = null;
    @property(cc.Node) startN: cc.Node = null;
    @property(cc.Sprite) display: cc.Sprite = null;

    readonly offsetList: cc.Vec2[] = [cc.v2(0, +1), cc.v2(0, -1), cc.v2(-1, 0), cc.v2(+1, 0)];
    readonly showHeight = 374;

    private originData: Uint8Array;
    private shapeData: Uint8Array;
    private compareData: Uint8Array;
    displayData: Uint8Array;

    private texture: cc.Texture2D;
    private spriteFrame: cc.SpriteFrame;
    config: TexFillConfigItem;

    private pen: cc.Node;

    private fillingIdxList: number[];
    private idxList: number[];
    private inFilling: boolean = false;
    private maxY: number = 0;
    private minY: number = 0;
    private disWidth: number = 0;
    private disHeight: number = 0;
    private isWin = false;
    public async init(level: number, pen: cc.Node) {
        this.pen = pen;
        this.pen.active = false;
        this.fillingIdxList = [];
        this.idxList = [];
        this.inFilling = false;
        this.isWin = false;
        this.config = await ConfigManager.ins.getTexFillConfigItem(level);

        let originTex = await LoaderTools.loadRes<cc.Texture2D>("game", "texture/fill/" + level + "_origin");
        this.disWidth = originTex.width;
        this.disHeight = originTex.height;
        this.origin.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(originTex);
        this.shape.getComponent(cc.Sprite).spriteFrame = await LoaderTools.loadSpriteFrame("game", "texture/fill/" + level + "_shape");
        this.startN.getComponent(cc.Sprite).spriteFrame = await LoaderTools.loadSpriteFrame("game", "texture/fill/" + level + "_start");

        this.node.setContentSize(this.disWidth, this.disHeight);
        this.origin.setContentSize(this.disWidth, this.disHeight);
        this.shape.setContentSize(this.disWidth, this.disHeight);
        this.startN.setContentSize(this.disWidth, this.disHeight);
        this.display.node.setContentSize(this.disWidth, this.disHeight);

        this.maxY = (this.disHeight - this.showHeight) / 2;
        this.minY = -this.maxY;
        this.node.y = this.maxY;

        this.originData = RenderUtil.getPixelsData(this.origin);
        this.shapeData = RenderUtil.getPixelsData(this.shape);
        this.displayData = RenderUtil.getPixelsData(this.startN);
        this.compareData = new Uint8Array(this.displayData.length);
        for (let i = 0; i < this.displayData.length; i++) {
            this.compareData[i] = this.displayData[i];
        }

        this.texture = new cc.Texture2D();
        this.spriteFrame = new cc.SpriteFrame();
        this.renderTexture(true);
    }

    stopAnim() {
        this.inFilling = false;
    }


    public fillShape(col: number) {
        for (let idx = 0; idx < this.config.shapes.length; idx++) {
            let it = this.config.shapes[idx];
            if (it.color == col && !this.idxList.includes(idx)) {
                GameMgr.ins.fillColorMap.set(idx, col);
                this.idxList.push(idx);
                this.fillingIdxList.push(idx);
                this.checkFill();
                break;
            }
        }
    }

    public getIdxByColor(col: number) {
        for (let idx = 0; idx < this.config.shapes.length; idx++) {
            let it = this.config.shapes[idx];
            if (it.color == col && !this.idxList.includes(idx)) {
                return idx;
            }
        }
    }

    public getPosByIdx(idx: number) {
        return this.display.node.convertToWorldSpaceAR(cc.v3(this.config.shapes[idx].posList[0]));
    }

    async checkFill() {
        this.checkWin();
        let stTime = Date.now();
        if (this.inFilling || this.fillingIdxList.length <= 0) return;
        let idx = this.fillingIdxList.splice(0, 1)[0];
        let posList = this.config.shapes[idx].posList;
        let color = this.config.shapes[idx].color;
        this.pen.getComponent(cc.Sprite).spriteFrame = await LoaderTools.loadSpriteFrame("game", `texture/pen_0${color}L`);
        this.pen.active = true;
        let posCount = posList.length;
        for (let j = 0; j < posList.length; j++) {
            let y = posList[j].y;
            this.node.y = Math.min(this.maxY, Math.max(this.minY, -y));
            await this.fillTextureColor(posList[j].x, y, posCount);
            if (!this.inFilling) break;
        }
        let cost = Date.now() - stTime;
        if (cost <= 500 && this.inFilling) {
            await Utils.sleep(0.5 - cost / 1000);
        }
        if (!this.inFilling) return;

        this.inFilling = false;
        this.pen.active = false;
        GameMgr.ins.completePaintColor(idx);
        this.checkFill();
    }

    private checkWin() {
        if (this.isWin) return;
        if (this.inFilling || this.fillingIdxList.length > 0) return;
        if (this.config.shapes.length > this.idxList.length) return;
        this.isWin = true;
        GameMgr.ins.win();
    }


    private async fillTextureColor(x: number, y: number, posCount: number) {
        this.inFilling = true;
        x = this.transToPixelX(this.disWidth, x);
        y = this.transToPixelY(this.disHeight, y);
        await this.fillColor(x, y, posCount);
    }

    private isCanSet(x: number, y: number, dispData: Uint8Array): boolean {
        let index = this.positionToBufferIndex(this.disWidth, x, y);
        return this.isIdxCanSet(index, dispData);
    }

    private transToPixelX(width: number, x: number): number {
        return Math.trunc(x + width * 0.5);
    }

    private transToPixelY(heigth: number, y: number): number {
        return Math.trunc(-y + heigth * 0.5);
    }

    private transToPosX(width: number, px: number) {
        return px - width * 0.5;
    }

    private transToPosY(heigth: number, py: number) {
        return heigth * 0.5 - py;
    }

    private positionToBufferIndex(width: number, x: number, y: number, colorSize = 4): number {
        return Math.trunc(x + y * width) * colorSize;
    }

    private isIdxCanSet(idx: number, dispdata: Uint8Array): boolean {
        if (idx < 0 || idx + 3 > this.shapeData.length || this.shapeData[idx + 3] > 1) return false;

        for (let i = idx; i < idx + 4; i++) {
            if (dispdata[i] != this.originData[i])
                return true;
        }
        return false;
    }

    private isIdxBoundary(idx: number): boolean {
        // 检查是否为边界：shapeData[idx + 3] > 1 表示边界
        if (idx < 0 || idx + 3 > this.shapeData.length) return false;
        return this.shapeData[idx + 3] > 1;
    }

    private canFillBoundary(idx: number, dispdata: Uint8Array): boolean {
        // 检查边界是否可以填充（边界且颜色不同）
        if (!this.isIdxBoundary(idx)) return false;

        for (let i = idx; i < idx + 4; i++) {
            if (dispdata[i] != this.originData[i])
                return true;
        }
        return false;
    }




    private setColor(x: number, y: number, data: Uint8Array): boolean {
        let index = this.positionToBufferIndex(this.disWidth, x, y);
        if (index < 0 || index + 3 > this.shapeData.length) return false;

        // 设置当前像素颜色
        for (let i = index; i < index + 4; i++) {
            data[i] = this.originData[i];
        }

        // 检查并填充周围的边界像素
        this.fillSurroundingBoundaries(x, y, data);

        return true;
    }

    private fillSurroundingBoundaries(x: number, y: number, data: Uint8Array): void {
        let checkRadius = 2; // 检查半径，可以根据线条宽度调整

        for (let dx = -checkRadius; dx <= checkRadius; dx++) {
            for (let dy = -checkRadius; dy <= checkRadius; dy++) {
                if (dx === 0 && dy === 0) continue; // 跳过中心点

                let nx = x + dx;
                let ny = y + dy;
                let nIndex = this.positionToBufferIndex(this.disWidth, nx, ny);

                // 检查是否为边界且可以填充
                if (this.canFillBoundary(nIndex, data)) {
                    // 直接填充边界像素
                    for (let i = nIndex; i < nIndex + 4; i++) {
                        if (i >= 0 && i < data.length) {
                            data[i] = this.originData[i];
                        }
                    }
                }
            }
        }
    }

    private async fillColor(x: number, y: number, posCount: number) {
        let colorPointList1 = [{ x, y }];
        let colorPointList2 = [];
        let totalCount = 0;
        while (colorPointList1.length > 0) {
            let lastX = 0, lastY = 0;
            for (let item of colorPointList1) {
                this.setColor(item.x, item.y, this.compareData);
                totalCount++;
                lastX = item.x, lastY = item.y;
                for (let offset of this.offsetList) {
                    let nx = item.x + offset.x, ny = item.y + offset.y;

                    if (!this.isCanSet(nx, ny, this.compareData)) {
                        continue;
                    }
                    if (colorPointList2.find(v => v.x == nx && v.y == ny) == null) {
                        colorPointList2.push({ x: nx, y: ny });
                    }
                }
            }
            colorPointList1 = colorPointList2;
            colorPointList2 = [];
        }

        colorPointList1 = [{ x, y }];
        colorPointList2 = [];
        let percount = Math.max(Math.floor(totalCount * posCount / 60), 1);
        let fillCount = 0;
        while (colorPointList1.length > 0) {
            let currentX = 0, currentY = 0;
            if (!this.inFilling) break;
            for (let item of colorPointList1) {
                if (!this.inFilling) break;
                this.setColor(item.x, item.y, this.displayData);
                currentX = item.x, currentY = item.y;
                for (let offset of this.offsetList) {
                    let nx = item.x + offset.x, ny = item.y + offset.y;

                    if (!this.isCanSet(nx, ny, this.displayData)) {
                        continue;
                    }
                    if (colorPointList2.find(v => v.x == nx && v.y == ny) == null) {
                        colorPointList2.push({ x: nx, y: ny });
                    }
                }

                if (++fillCount >= percount) {
                    fillCount = 0;
                    let posX = this.transToPosX(this.disWidth, currentX);
                    let posY = this.transToPosY(this.disHeight, currentY);
                    let ws = this.display.node.convertToWorldSpaceAR(cc.v3(posX, posY));
                    let loc = this.pen.parent.convertToNodeSpaceAR(ws);
                    this.pen.position = loc;

                    this.renderTexture();
                    await Utils.sleep(0.01);
                    if (!this.inFilling) break;
                }
            }
            colorPointList1 = colorPointList2;
            colorPointList2 = [];
        }
        this.renderTexture();
    }

    private renderTexture(force: boolean = false) {
        if (!force && !this.inFilling) return;
        this.texture.initWithData(this.displayData, cc.Texture2D.PixelFormat.RGBA8888, this.disWidth, this.disHeight);
        this.spriteFrame.setTexture(this.texture);
        this.display.spriteFrame = this.spriteFrame;
    }

    protected onDestroy(): void {
        this.originData = null;
        this.displayData = null;
        this.originData = null;
    }

}