import GameMgr from "../managers/GameMgr";
import { GameData } from "../model/GameModel";
import BlockItem from "./BlockItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class RemoveItem extends cc.Component {

    // 直接销毁这个block
    onBeginContact(contact: cc.PhysicsContact, selfCollider: cc.Collider, otherCollider: cc.Collider) {
        let idx = GameData.blockItems.indexOf(otherCollider.node);
        if (idx >= 0 && cc.isValid(otherCollider.node) && otherCollider.node.active){
            otherCollider.node.active = false;
            GameMgr.ins.onRemoveBlock(otherCollider.node.getComponent(BlockItem).blockColor);
        }
    }
}
