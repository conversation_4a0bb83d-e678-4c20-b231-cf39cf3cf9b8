import { LogUtil } from "../frame/utils/LogUtil";
import { DeviceType, ShapeType } from "../GameConst";
import GameMgr from "../managers/GameMgr";
import { GameData } from "../model/GameModel";
import PinItem from "./PinItem";


const { ccclass, property } = cc._decorator;


@ccclass
export default class BlockItem extends cc.Component {
    private circleCenterPoints: cc.Vec2[] = [];

    blockColor: cc.Color = null;

    // 获取所有子节点的位置作为圆心位置
    private getChildPoints() {
        let num = 0;
        this.node.children.forEach((child) => {
            if (!child.active) return;
            let pos = child.getPosition();

            if (num == 1)
                this.circleCenterPoints.push(pos);
            num++;
        });
    }

    protected onLoad(): void {
        GameData.blockItems.push(this.node);
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    onTouchStart(event) {
        if(GameData.hammer){
            GameMgr.ins.brokenBlock(this.node);
        }
    }

    setup() {
        this.init();
        this.node.width = this.realWidth != 0 ? this.realWidth : this.node.width;
        this.node.height = this.realHeight != 0 ? this.realHeight : this.node.height;
        this.initStartPolys();

        let xr = this.node.width / this.oriSize.x;
        let yr = this.node.height / this.oriSize.y;
        this.polyCollider.points.forEach(it => {
            it.x *= xr;
            it.y *= yr;
        });

        try {
            this.polyCollider.points = this.polyCollider.points;
            this.polyCollider.friction = 0;
            this.polyCollider.restitution = 0.5;
            this.polyCollider.apply();
        } catch (e) {
            LogUtil.error(e);
        }
    }

    check() {
        let num = 0;
        let displayNode: cc.Node = null;
        this.node.children.forEach((child) => {
            if (!child.active || child.getComponent(PinItem) == null) return;
            num++;
            displayNode = child;
        });
        if (num == 1) {

            this.circleCenterPoints = [];
            this.circleCenterPoints.push(displayNode.getPosition());
            this.initPolys(this.circleCenterPoints);
            this.rigidbody.type = cc.RigidBodyType.Dynamic;
        }
    }

    polyCollider: cc.PhysicsPolygonCollider = null;
    @property({ type: ShapeType }) shapeType: number = ShapeType.Strip;
    @property(cc.Vec2) oriSize: cc.Vec2 = null;
    @property shape: number = 0;
    rigidbody: cc.RigidBody;
    startPoly: cc.Vec2[] = [];
    holePolys: cc.Vec2[][] = [];
    isEditor: boolean;
    deviceType: DeviceType = DeviceType.Default;
    realLayer: number = 0;
    realWidth: number = 0;
    realHeight: number = 0;

    // 这里的woodConf是配置文件中得到的木板数据
    public async init() {
        this.polyCollider = this.node.getComponent(cc.PhysicsPolygonCollider);
        this.oriSize = cc.v2(this.node.width, this.node.height)
        this.rigidbody = this.node.getComponent(cc.RigidBody);
        this.rigidbody.gravityScale = 2.6;
        this.node.scale = 1;
        this.rigidbody.enabledContactListener = true;
        this.rigidbody.type = cc.RigidBodyType.Static;
    }

    // 这里是实际计算hole的算法，只需要将洞的坐标给进来就行了
    public async initPolys(posList: cc.Vec2[]) {
        let polys = []
        let sortSl = [];

        let piece = GameMgr.ins.circleFregmentCount;
        let angle = 2 * Math.PI / piece;
        let radius = Math.ceil(GameMgr.ins.circleRadius / Math.cos(angle / 2));
        for (let i = 0; i < posList.length; i++) {
            let pos = posList[i];
            let regions: cc.Vec2[] = [];
            for (let index = 0; index < piece; index++) {
                const p = 2 * Math.PI * index / piece;
                const x = pos.x + radius * Math.cos(p);
                const y = pos.y + radius * Math.sin(p);
                regions.push(cc.v2(x, y));
            }
            sortSl.push([pos, regions]);
        }
        sortSl.sort((a, b) => {
            if (a[0].x == b[0].x) return a[0].y - b[0].y;
            return a[0].x - b[0].x;
        });
        sortSl.forEach((sl) => {
            polys.push(sl[1]);
        });
        this.holePolys = polys;

        let points: cc.Vec2[] = [];

        if (posList.length == 1 || this.shapeType == 1) {
            points = this.genPointsPath(0, 0, this.startPoly, this.holePolys[0]);
        } else {
            let list = []
            for (let j = 0; j < polys.length; j++) {
                let minDistance = -1;
                let minPos: cc.Vec2;
                let minI = -1;
                let minM = -1;
                for (let i = 0; i < this.startPoly.length; i++) {
                    let v1 = this.startPoly[i];
                    let i2 = i == this.startPoly.length - 1 ? 0 : i + 1;
                    let v2 = this.startPoly[i2];
                    let pos = sortSl[j][0];
                    let p = this.calcShortestPoint(pos, v1, v2);
                    let dis = this._distance(pos, p);
                    if (minDistance > dis || minDistance == -1) {
                        let pp = this.calMinPos(polys[j], p);
                        let intersect = false;
                        for (let k = 0; k < polys.length; k++) {
                            if (k == j) continue;
                            if (cc.Intersection.linePolygon(pp, p, polys[k])) {
                                intersect = true;
                                break;
                            }
                        }
                        for (let n = 0; n < list.length; n++) {
                            let tp = list[n][2];
                            let tpIdx = list[n][1];
                            if (cc.Intersection.lineLine(pp, p, tp, polys[n][tpIdx])) {
                                intersect = true;
                                break;
                            }
                        }
                        if (!intersect) {
                            minDistance = dis;
                            minPos = p;
                            minI = i;
                            minM = polys[j].indexOf(pp);
                        }
                    }
                }
                list.push([minI, minM, minPos, j]);
            }

            // 找到n个位置不同的点
            list.sort((a, b) => {
                if (a[0] == b[0]) {
                    let idx = a[0];
                    let ap = this._distance(a[2], this.startPoly[idx]);
                    let bp = this._distance(b[2], this.startPoly[idx]);
                    return ap - bp;
                }
                return a[0] - b[0];
            });

            let lastIdx = -1;
            for (let i = 0; i < list.length; i++) {
                let idx = list[i][0];
                let equal = this.startPoly[idx] == list[i][2];
                for (let j = lastIdx + 1; j <= idx; j++) {
                    points.push(this.startPoly[j])
                }
                if (!equal) {
                    points.push(list[i][2]);
                }
                let minM = list[i][1];
                let pj = list[i][3];
                for (let k = minM; k < polys[pj].length; k++) {
                    points.push(polys[pj][k]);
                }
                for (let k = 0; k <= minM; k++) {
                    if (k != minM)
                        points.push(polys[pj][k]);
                    else {
                        points.push(polys[pj][k]);
                    }
                }
                points.push(list[i][2]);
                lastIdx = idx;
            }
            for (let j = lastIdx + 1; j < this.startPoly.length; j++) {
                points.push(this.startPoly[j]);
            }

            let plist = [];
            points.forEach((p) => {
                plist.push(cc.v2(p.x, p.y));
            })
        }

        try {
            this.polyCollider.points = points;
            this.polyCollider.friction = 0;
            this.polyCollider.restitution = 0.5;
            this.polyCollider.apply();
        } catch (e) {
            LogUtil.error(e);
        }

        // 上面是计算洞的点的算法，这里就是初始化洞的表现参数，例如Sprite还有材质的参数
        // for (let i = 0; i < posList.length; i++) {
        //     let pos = posList[i];
        //     this.initHole(pos, i, radius);
        // }
    }


    calMinPos(points: cc.Vec2[], dot: cc.Vec2): cc.Vec2 {
        let mindis = -1;
        let minPos: cc.Vec2;
        for (let m = 0; m < points.length; m++) {
            let pos = points[m];
            let dis = this._distance(pos, dot);
            if (mindis > dis || mindis == -1) {
                minPos = pos;
                mindis = dis;
            }
        }
        return minPos;
    }


    calcShortestPoint(op: cc.Vec2, op1: cc.Vec2, op2: cc.Vec2) {
        // 做垂足
        var p1p2 = op2.sub(op1);
        if (p1p2.magSqr() == 0) {
            return op1;
        }
        var p1p = op.sub(op1);
        var p2p = op.sub(op2);

        var proj_pp2_p1p2 = p2p.project(cc.v2(p1p2.x, p1p2.y));

        var ot = op2.add(proj_pp2_p1p2);    // t的坐标
        // 计算距离
        var pt = op.sub(ot);
        var tp1 = op1.sub(ot);
        var tp2 = op2.sub(ot);
        var len2_pp1 = p1p.magSqr();
        var len2_pp2 = p2p.magSqr();
        var len2_pt = pt.magSqr();
        var pos = [op1, op2, ot][[len2_pp1, len2_pp2, len2_pt].indexOf(Math.min(len2_pp1, len2_pp2, len2_pt))];
        // 判断垂足点在线段内
        if (tp1.magSqr() + tp2.magSqr() > p1p2.magSqr()) {
            pos = [op1, op2][[len2_pp1, len2_pp2].indexOf(Math.min(len2_pp1, len2_pp2))];
        }
        return pos;
    }

    genPointsPath(lIndex: number, n: number, ll: cc.Vec2[], rr: cc.Vec2[]): cc.Vec2[] {
        let points: cc.Vec2[] = []
        if (!rr) {
            for (let i = lIndex; i < ll.length; i++) {
                points.push(ll[i]);
            }
            for (let i = 0; i <= lIndex; i++) {
                points.push(ll[i]);
            }
            return points;
        }

        let minI = -1;
        let minJ = -1;
        let minDis = -1;

        for (let i = 0; i < ll.length; i++) {
            for (let j = 0; j < rr.length; j++) {
                let dis = this._distance(ll[i], rr[j])
                if (minDis > dis || minDis < 0) {
                    let intersect = false;
                    if (n == 0) {
                        for (let m = 0; m < this.holePolys.length; m++) {
                            if (m == n) continue;
                            if (cc.Intersection.linePolygon(ll[i], rr[j], this.holePolys[m])) {
                                intersect = true;
                                break;
                            }
                        }
                        // if (this.woodConf.typ == 1 && ll[i].x >= 0) {
                        //     intersect = true;
                        // }
                    }
                    if (!intersect) {
                        minDis = dis;
                        minI = i;
                        minJ = j;
                    }
                }
            }
        }

        n++;
        if (lIndex == minI) {
            points.push(ll[lIndex]);
            let list = this.genPointsPath(minJ, n, rr, this.holePolys[n]);
            points = points.concat(list);
            for (let i = minI; i < ll.length; i++) {
                points.push(ll[i]);
            }
            for (let i = 0; i <= minI; i++) {
                points.push(ll[i]);
            }
            return points;

        }

        if (lIndex > minI) {
            for (let i = lIndex; i < ll.length; i++) {
                points.push(ll[i]);
            }
            for (let i = 0; i <= minI; i++) {
                points.push(ll[i]);
            }
            let list = this.genPointsPath(minJ, n, rr, this.holePolys[n]);
            points = points.concat(list);

            for (let i = minI; i <= lIndex; i++) {
                points.push(ll[i]);
            }
            return points;
        }

        if (lIndex < minI) {
            for (let i = lIndex; i <= minI; i++) {
                points.push(ll[i]);
            }
            let list = this.genPointsPath(minJ, n, rr, this.holePolys[n]);
            points = points.concat(list);

            for (let i = minI; i < ll.length; i++) {
                points.push(ll[i]);
            }
            for (let i = 0; i <= lIndex; i++) {
                points.push(ll[i]);
            }
            return points;
        }
    }

    _distance(start: cc.Vec2, end: cc.Vec2) {
        let v = cc.v2(start.x - end.x, start.y - end.y);
        return v.x * v.x + v.y * v.y;
    }

    protected initStartPolys(): void {
        // 需要将points 转成顺时针
        this.startPoly = [];

        if (this.shapeType == ShapeType.Strip && !this.isEditor && this.deviceType == DeviceType.Low) {
            let h = this.node.height / 2;
            let w = this.node.width / 2 - 1;
            this.startPoly = [cc.v2(-w, h), cc.v2(w, h), cc.v2(w, -h), cc.v2(-w, -h)];
        } else {
            let xr = this.node.width / this.oriSize.x;
            let yr = this.node.height / this.oriSize.y;
            for (let j = this.polyCollider.points.length - 1; j >= 0; j--) {
                let v = this.polyCollider.points[j];
                this.startPoly.push(cc.v2(v.x * xr, v.y * yr));
            }
        }
    }

    isCirclePolygon(pos: cc.Vec2, radius: number = 70): boolean {
        return cc.Intersection.polygonCircle(this.startPoly, { position: pos, radius: radius });
    }
}
