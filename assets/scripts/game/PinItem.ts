import { audioManager } from "../frame/manager/audioManager";
import ConfigMgr from "../managers/ConfigMgr";
import GameMgr from "../managers/GameMgr";
import { GameData } from "../model/GameModel";
import { PinColor } from "../model/LevelConfig";
import BlockItem from "./BlockItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PinItem extends cc.Component {
    @property({ type: PinColor })
    pinColor: number = PinColor.Red;

    realLayer: number = 0;

    sprite: cc.Sprite = null;
    shakeNode: cc.Node = null;

    protected start(): void {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.sprite = this.getComponent(cc.Sprite);
    }

    private onTouchStart(event: cc.Event.EventTouch): void {
        if(!this.getComponent(cc.Sprite).enabled) return;
        if (GameData.hasCovered(this.node)) {
            console.log("has covered");
            this.playShakeAnim();
            return;
        }
        if (ConfigMgr.ins != null || GameMgr.ins.collectColor(this.pinColor, this.node)) {
            this.node.active = false;
            this.node.parent.getComponent(BlockItem).check();
        }
    }

    private playShakeAnim() {
        if (this.shakeNode == null) {
            this.shakeNode = cc.instantiate(this.node);
            this.shakeNode.parent = this.node;
            this.shakeNode.removeComponent(cc.PhysicsCircleCollider);
            this.shakeNode.removeComponent(cc.RigidBody);
            this.shakeNode.removeComponent(PinItem);
            this.shakeNode.setPosition(0, 0);
            this.shakeNode.angle = 0;
            this.shakeNode.active = false;
        }
        this.shakeNode.stopAllActions();
        this.shakeNode.angle = 0;
        this.shakeNode.active = true;
        this.sprite.enabled = false;
        this.shake(this.shakeNode, 0.2, 20);
    }

    shake(target: cc.Node, duration: number = 0.3, magnitude: number = 5) {
        const originalAngle = target.angle;
        const times = 2; // 抖动次数
        const tweens: cc.Tween<cc.Node>[] = [];

        for (let i = 0; i < times; i++) {
            const rotateAngle = (Math.random() * 2 - 1) * magnitude;
            tweens.push(
                cc.tween().to(duration / (times * 2), { angle: rotateAngle }),
                cc.tween().to(duration / (times * 2), { angle: originalAngle }),
                cc.tween().to(duration / (times * 2), { angle: -rotateAngle }),
                cc.tween().to(duration / (times * 2), { angle: originalAngle })
            );
        }
        // 组合所有 tween 并执行
        cc.tween(target)
            .then(cc.tween().sequence(...tweens as [cc.Tween<cc.Node>, ...cc.Tween<cc.Node>[]]))
            .call(() => {
                this.shakeNode.active = false;
                this.sprite.enabled = true;
            })
            .start();
    }
}
