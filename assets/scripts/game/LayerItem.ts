import ConfigMgr from "../managers/ConfigMgr";
import { BlockItemConfig, PinItemConfig } from "../model/LevelConfig";
import BlockItem from "./BlockItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LayerItem extends cc.Component {

    @property
    layerIndex: number = 0;
    protected onLoad(): void {
        console.log("LayerItem on load");
        ConfigMgr.ins.layerNodes.push(this.node);
    }

    getBlockConfigs(): BlockItemConfig[] {
        let blockConfigs: BlockItemConfig[] = [];
        this.node.children.forEach((block) => {
            if(!block.active) return;
            let blockItemConf = new BlockItemConfig();
            blockItemConf.x = parseFloat(block.getPosition().x.toFixed(3));
            blockItemConf.y = parseFloat(block.getPosition().y.toFixed(3));
            blockItemConf.type = block.getComponent(BlockItem).shapeType;
            blockItemConf.shape = block.getComponent(BlockItem).shape;
            blockItemConf.pins = [];
            blockItemConf.rotation = block.angle;
            blockItemConf.width = parseFloat(block.width.toFixed(3));
            blockItemConf.height = parseFloat(block.height.toFixed(3));
            block.children.forEach((pin) => {
                let pinItemConf = new PinItemConfig();
                pinItemConf.x = parseFloat(pin.getPosition().x.toFixed(3));
                pinItemConf.y = parseFloat(pin.getPosition().y.toFixed(3));
                pinItemConf.width = pin.width;
                pinItemConf.height = pin.height;
                blockItemConf.pins.push(pinItemConf);

            });
            blockConfigs.push(blockItemConf);
        });
        return blockConfigs;
    }
}
