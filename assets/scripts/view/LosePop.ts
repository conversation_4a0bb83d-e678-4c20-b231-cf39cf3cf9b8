import { audioManager } from "../frame/manager/audioManager";
import { UIView } from "../frame/ui/UIView";
import GameMgr from "../managers/GameMgr";
import { TrackLogMgr } from "../managers/TrackLogMgr";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("UI/LosePop")
export default class LosePop extends UIView {
    @property(cc.Node) closeBtn: cc.Node = null;
    @property(cc.Sprite) icon: cc.Sprite = null;
    @property(cc.Node) frame: cc.Node = null;
    @property(cc.ProgressBar) bar: cc.ProgressBar = null;
    @property(cc.Label) progressLab: cc.Label = null;

    clicked = false;
    public onOpen(fromUI: number, ...args: any[]): void {
        this.closeBtn.on("click", this.onClickClose, this);

        let textureData = args[0].textureData as Uint8Array;
        let width = args[0].width as number;
        let height = args[0].height as number;
        let progress = args[0].progress as number;

        let duration = args[0].duration as number;
        TrackLogMgr.addLog("show_lose", { duration: duration });

        this.bar.progress = progress;
        this.progressLab.string = Math.floor(progress * 100) + "%";

        let texture = new cc.Texture2D();
        texture.initWithData(textureData, cc.Texture2D.PixelFormat.RGBA8888, width, height);
        this.icon.node.width = width;
        this.icon.node.height = height;
        this.icon.spriteFrame = new cc.SpriteFrame(texture);
        this.frame.width = width * this.icon.node.scale + 20;
        this.frame.height = height * this.icon.node.scale + 20;

        audioManager.instance.playSound("no_space");

    }

    private onClickClose() {
        if (this.clicked) return;
        this.clicked = true;

        GameMgr.ins.startGame();
        TrackLogMgr.addLog("lose_page_click", { type: 'try' });
        this.onBtn_close();
    }


}
