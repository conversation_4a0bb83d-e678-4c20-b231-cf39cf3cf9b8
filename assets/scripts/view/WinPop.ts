import RenderUtil from "../colorfill/RenderUtil";
import { LocalizationMgr } from "../frame/localization/LocalizationMgr";
import { audioManager } from "../frame/manager/audioManager";
import { UIView } from "../frame/ui/UIView";
import { LoaderTools } from "../frame/utils/LoaderTools";
import { RandomUtil } from "../frame/utils/RandomUtil";
import { ConfigManager } from "../managers/ConfigManager";
import GameMgr from "../managers/GameMgr";
import { TrackLogMgr } from "../managers/TrackLogMgr";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("UI/WinPop")
export default class WinPop extends UIView {
    @property(cc.Node) continueBtn: cc.Node = null;
    @property(cc.Node) skipBtn: cc.Node = null;
    @property(cc.Node) iconBgNode: cc.Node = null;
    @property(cc.Label) title: cc.Label = null;
    @property(cc.Node) origin: cc.Node = null;
    @property(cc.Node) shape: cc.Node = null;
    @property(cc.Node) startN: cc.Node = null;
    @property(cc.Sprite) display: cc.Sprite = null;

    private originData: Uint8Array;
    private shapeData: Uint8Array;
    private displayData: Uint8Array;
    private texture: cc.Texture2D;
    private spriteFrame: cc.SpriteFrame;
    private originSp: cc.SpriteFrame;

    private width: number;
    private height: number;
    private skipAnim = false;

    onOpen(fromUI: number, ...args: any[]): void {
        this.skipBtn.on("click", this.onClickSkip, this);
        this.continueBtn.on("click", this.onClickContinue, this);

        this.continueBtn.active = false;
        let level = args[0].level as number;
        let duration = args[0].duration as number;
        TrackLogMgr.addLog("show_win", { level: level, duration: duration });

        this.title.string = LocalizationMgr.ins.t("LevelCompleteText" + RandomUtil.randomRange(1, 10));
        this.initIcon(level)

        audioManager.instance.playSound("win");
    }

    async initIcon(level: number) {
        let config = await ConfigManager.ins.getTexFillConfigItem(level);
        let originTex = await LoaderTools.loadRes<cc.Texture2D>("game", "texture/fill/" + level + "_origin");
        this.width = originTex.width;
        this.height = originTex.height;

        this.originSp = new cc.SpriteFrame(originTex);
        this.origin.getComponent(cc.Sprite).spriteFrame = this.originSp;
        this.shape.getComponent(cc.Sprite).spriteFrame = await LoaderTools.loadSpriteFrame("game", "texture/fill/" + level + "_shape");
        this.startN.getComponent(cc.Sprite).spriteFrame = await LoaderTools.loadSpriteFrame("game", "texture/fill/" + level + "_start");

        this.origin.setContentSize(this.width, this.height);
        this.shape.setContentSize(this.width, this.height);
        this.startN.setContentSize(this.width, this.height);
        this.display.node.setContentSize(this.width, this.height);
        this.iconBgNode.setContentSize(this.width * this.display.node.scale + 20, this.height * this.display.node.scale + 20);

        this.originData = RenderUtil.getPixelsData(this.origin);
        this.shapeData = RenderUtil.getPixelsData(this.shape);
        this.displayData = RenderUtil.getPixelsData(this.startN);

        this.texture = new cc.Texture2D();
        this.spriteFrame = new cc.SpriteFrame();
        this.renderTexture();

        for (let i = 0; i < config.shapes.length; i++) {
            let posList = config.shapes[i].posList;
            if (this.skipAnim) break;
            for (let j = 0; j < posList.length; j++) {
                if (this.skipAnim) break;
                await this.fillTextureColor(posList[j].x, posList[j].y);
            }
        }
        this.display.spriteFrame = this.originSp;
        this.skipBtn.active = false;
        this.continueBtn.active = true;
    }

    private onClickSkip() {
        this.skipAnim = true;
        this.skipBtn.active = false;
        this.continueBtn.active = true;
        TrackLogMgr.addLog("win_page_click", { type: "skip" });
    }

    private async onClickContinue() {
        await GameMgr.ins.preloadAssets();
        GameMgr.ins.startGame();
        TrackLogMgr.addLog("win_page_click", { type: "Continue" });
        this.onBtn_close();
        this.originData = null;
        this.displayData = null;
        this.originData = null;
    }

    private async fillTextureColor(x: number, y: number) {
        x = this.translateX(this.width, x);
        y = this.translateY(this.height, y);
        await this.fillColor(x, y);
    }

    private isCanSet(x: number, y: number): boolean {
        let index = this.positionToBufferIndex(this.width, x, y);
        return this.isIdxCanSet(index);
    }

    private translateX(width: number, x: number): number {
        return Math.trunc(x + width * 0.5);
    }

    private translateY(heigth: number, y: number): number {
        return Math.trunc(-y + heigth * 0.5);
    }

    private positionToBufferIndex(width: number, x: number, y: number, colorSize = 4): number {
        return Math.trunc(x + y * width) * colorSize;
    }

    private isIdxCanSet(idx: number): boolean {
        if (idx < 0 || idx + 3 > this.shapeData.length || this.shapeData[idx + 3] > 1) return false;

        for (let i = idx; i < idx + 4; i++) {
            if (this.displayData[i] != this.originData[i])
                return true;
        }
        return false;
    }


    private setColor(x: number, y: number): boolean {
        let index = this.positionToBufferIndex(this.width, x, y);
        if (index < 0 || index + 3 > this.shapeData.length) return false;
        for (let i = index; i < index + 4; i++) {
            this.displayData[i] = this.originData[i];
        }
        return true;
    }

    private async fillColor(x: number, y: number) {
        let colorPointList1 = [{ x, y }];
        let colorPointList2 = [];
        let fillCount = 0;
        while (colorPointList1.length > 0) {
            if (this.skipAnim) break;

            for (let item of colorPointList1) {
                this.setColor(item.x, item.y);
                x = item.x, y = item.y;
                for (let offset of [cc.v2(0, +1), cc.v2(0, -1), cc.v2(-1, 0), cc.v2(+1, 0)]) {
                    let nx = item.x + offset.x, ny = item.y + offset.y;
                    if (!this.isCanSet(nx, ny)) {
                        continue;
                    }
                    if (colorPointList2.find(v => v.x == nx && v.y == ny) == null) {
                        colorPointList2.push({ x: nx, y: ny });
                    }
                }
            }
            if (++fillCount >= 50) {
                fillCount = 0;

                this.renderTexture();
                await this.sleep(0.01);
            }
            colorPointList1 = colorPointList2;
            colorPointList2 = [];
        }
        this.renderTexture();
    }

    private async sleep(time: number) {
        return new Promise((resolve) => {
            cc.tween(this.node)
                .delay(time)
                .call(() => {
                    resolve(null);
                })
                .start();
        });
    }


    private renderTexture() {
        if (this.skipAnim) {
            this.display.spriteFrame = this.originSp;
            return;
        }
        this.texture.initWithData(this.displayData, cc.Texture2D.PixelFormat.RGBA8888, this.width, this.height);
        this.spriteFrame.setTexture(this.texture);
        this.display.spriteFrame = this.spriteFrame;
    }
}
