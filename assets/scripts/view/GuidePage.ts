import { ScreenType, uiManager } from "../frame/ui/UIManager";
import { UIView } from "../frame/ui/UIView";
import { GuideData, GuideStep } from "../managers/GuideManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GuidePage extends UIView {
    @property(cc.Node) mask1: cc.Node = null;
    @property(cc.Node) mask2: cc.Node = null;
    @property(cc.Node) middle: cc.Node = null;
    @property(cc.Node) finger: cc.Node = null;
    @property(cc.Node) finger2: cc.Node = null;
    @property(cc.Node) tips: cc.Node = null;
    @property(cc.RichText) tipsLabel: cc.RichText = null;

    guideData: GuideData = null;
    protected onLoad(): void {
        this.node.getComponent(cc.Widget).updateAlignment();
    }

    protected onDisable(): void {
        this.removeEvent();
    }

    public onOpen(fromUI: number, ...args: any[]): void {
        this.guideData = args[0];
        this.updateUI();
        this.addEvent();
        // this.node._touchListener.setSwallowTouches(true);
        // console.log(this.node._touchListener)
    }

    protected updateUI(): void {
        let finger: cc.Node;
        if (this.guideData.useFinger === 1) {
            this.finger.active = true;
            this.finger2.active = false;
            finger = this.finger;
        } else {
            this.finger.active = false;
            this.finger2.active = true;
            finger = this.finger2;
        }

        if (uiManager.screenType == ScreenType.Pad) {
            this.finger.scale = 0.6;
            this.tips.scale = 0.6;
        }

        this.tipsLabel.string = `<b>${this.guideData.tips}</b>`;
        this.tips.active = this.guideData.tips != "";
        this.tips.setContentSize(this.tipsLabel.node.width + 60, this.tipsLabel.node.height + 40)

        if (this.guideData.target1) {
            let rect1 = this.guideData.target1.getBoundingBoxToWorld();
            let pos = this.guideData.target1.convertToWorldSpace(cc.v2(0, 0));
            let pos1 = this.node.convertToNodeSpace(pos).sub(this.guideData.guideSubStep == GuideStep.NewGame ? cc.v2(0, rect1.height) : cc.Vec2.ZERO);
            pos1.x += cc.winSize.width / 2;
            pos1.y += cc.winSize.height / 2;
            this.mask1.x = pos1.x + rect1.width * this.mask1.anchorX;
            this.mask1.y = pos1.y;
            this.mask1.width = rect1.width + (this.guideData.guideSubStep == GuideStep.NewGame ? rect1.width / 2 : 0);
            this.mask1.height = rect1.height + (this.guideData.guideSubStep == GuideStep.NewGame ? rect1.height * 2 : 0);

            if (this.guideData.useFinger === 1) {
                if (this.guideData.isHandInMiddle) {
                    this.finger.position = cc.v3(pos1.x + rect1.width / 2, pos1.y + rect1.height / 2);
                } else {
                    this.finger.position = cc.v3(pos1.x + rect1.width, pos1.y);
                    if (this.finger.x > cc.winSize.width - 50) {
                        this.finger.x = cc.winSize.width - 50;
                    } else if (rect1.width / 2 < 50) {
                        this.finger.x += 50;
                    }
                }
            } else if (this.guideData.useFinger === 2) {
                finger.position = cc.v3(pos1.x + rect1.width / 2, pos1.y + rect1.height + 160);
                finger.stopAllActions();
                cc.tween(finger)
                    .by(0.6, { y: -60 }, { easing: 'cubicIn' })
                    .by(0.6, { y: 60 }, { easing: 'cubicOut' })
                    .union().repeatForever()
                    .start();
            }

            this.tips.y = pos1.y - this.tips.height / 2 - 20;
            this.tips.x = pos1.x + rect1.width / 2;

            if (this.tips.y - this.tips.height / 2 < 0) this.tips.y = this.tips.height / 2;
            if (this.tips.x - this.tips.width / 2 < 0) this.tips.x = this.tips.width / 2 + 10;
        } else {
            this.mask1.y = 3000;
        }
        if (this.guideData.target2) {
            let rect2 = this.guideData.target2.getBoundingBoxToWorld();
            let pos = this.guideData.target2.convertToWorldSpace(cc.v2(0, 0));
            let pos2 = this.node.convertToNodeSpace(pos);
            this.mask2.x = pos2.x + rect2.width * this.mask1.anchorX;
            this.mask2.y = pos2.y + rect2.height;
            this.mask2.width = rect2.width;
            this.mask2.height = rect2.height;
        } else {
            this.mask2.y = -3000;
        }
        this.middle.y = (this.mask1.y + this.mask2.y) / 2;
        this.middle.height = this.mask1.y - this.mask2.y;

        if (this.guideData.tipsPos) this.tips.setPosition(this.guideData.tipsPos);

        this.middle.opacity = this.guideData.maskOpc;
        this.mask1.getChildByName("bg").opacity = this.guideData.maskOpc;
        this.mask2.getChildByName("bg").opacity = this.guideData.maskOpc;

        if (this.guideData.needOpenAni)
            this.guideOpenAni();
        else {
            this.mask1.opacity = 255;
            this.mask2.opacity = 255;
            finger.active = true;
        }
    }


    guideOpenAni() {
        this.mask1.opacity = 0;
        this.middle.opacity = 0;
        this.mask2.opacity = 0;

        this.mask1.runAction(cc.fadeTo(0.5, 255));
        this.middle.runAction(cc.fadeTo(0.5, this.guideData.maskOpc));
        this.mask2.runAction(cc.fadeTo(0.5, 255));

        if (this.guideData.tips != "") {
            const finger = this.guideData.useFinger === 1 ? this.finger : this.finger2;
            finger.active = false;
            this.tips.active = true;
            this.tips.opacity = 180;
            cc.tween(this.tips).set({ scale: 0.4 })
                .to(0.4, { scale: 1, opacity: 255 }, { easing: "backOut" }).call(() => {
                    if (finger)
                        finger.active = true;
                }).start();
        }
    }

    private BlockMouseEvents = [
        'mousedown', 'mousemove', 'mouseup',
        'mouseenter', 'mouseleave', 'mousewheel'];
    private BlockTouchEvents = ['touchstart', 'touchmove', 'touchend'];


    addEvent() {
        if (!this.guideData.isOnlyShow) {
            for (var i = 0; i < this.BlockMouseEvents.length; i++) {
                this.node.on(this.BlockMouseEvents[i], this.stopMousePropagation, this);
            }
            for (var i = 0; i < this.BlockTouchEvents.length; i++) {
                this.node.on(this.BlockTouchEvents[i], this.stopTouchPropagation, this);
            }

            this.node.on(cc.Node.EventType.TOUCH_END, this.onTouch, this);
        } else {
            //@ts-ignore
            this.node?._touchListener?.setSwallowTouches(true);
        }
    }

    removeEvent() {
        if (!this.guideData.isOnlyShow) {
            for (var i = 0; i < this.BlockMouseEvents.length; i++) {
                this.node.off(this.BlockMouseEvents[i], this.stopMousePropagation, this);
            }
            for (var i = 0; i < this.BlockTouchEvents.length; i++) {
                this.node.off(this.BlockTouchEvents[i], this.stopTouchPropagation, this);
            }

            this.node.off(cc.Node.EventType.TOUCH_END, this.onTouch, this);
        } else {
            //@ts-ignore
            this.node?._touchListener?.setSwallowTouches(false);
        }
    }

    stopMousePropagation(event: cc.Event.EventMouse) {
        let index = this.testHit(event.getLocation());
        if (index == 1) {
            //可以分发点击事件
            this.guideData.target1.dispatchEvent(event);
        }
        else if (index == 2) {
            //可以分发点击事件
            this.guideData.target2.dispatchEvent(event);
        }
        else {
            event.stopPropagation();
        }
    }

    stopTouchPropagation(event: cc.Event.EventTouch) {
        let index = this.testHit(event.touch.getLocation());
        if (index == 1) {
            //可以分发点击事件
            this.guideData.target1.dispatchEvent(event);
        }
        else if (index == 2) {
            //可以分发点击事件
            this.guideData.target2.dispatchEvent(event);
        }
        else {
            event.stopPropagation();
        }
    }

    testHit(pt: cc.Vec2): number {
        if (this.guideData.target1 && this.mask1.getBoundingBox().contains(pt)) return 1;
        if (this.guideData.target2 && this.mask2.getBoundingBox().contains(pt)) return 2;
        return 0;
    }

    private onTouch(event: cc.Event.EventTouch) {
        // if (this.guideData.isOnlyShow) {
        //     // console.log(event)

        //     // this.guideData.closeCb?.();
        //     // this.onBtn_close();
        //     return;
        // }

        if (this.guideData.isFullClick) {
            this.onBtn_close();
            this.guideData.closeCb?.();
            const evs = this.guideData.target1?.getComponent(cc.Button)?.clickEvents;
            if (evs) {
                evs.forEach(ev => {
                    ev.emit([null, ev.customEventData]);
                })
            }
        } else {
            let index = this.testHit(event.touch.getLocation());
            if (index) {
                this.onBtn_close();
                this.guideData.closeCb?.();
            }
        }
    }
}
