import { LocalizationMgr } from "../frame/localization/LocalizationMgr";
import { audioManager } from "../frame/manager/audioManager";
import { UIView } from "../frame/ui/UIView";
import GameMgr from "../managers/GameMgr";
import { TrackLogMgr } from "../managers/TrackLogMgr";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("UI/RevivePop")
export default class RevivePop extends UIView {
    @property(cc.Node) closeBtn: cc.Node = null;
    @property(cc.Node) reviveBtn: cc.Node = null;
    @property(cc.RichText) progressLab: cc.RichText = null;

    clicked = false;

    public onOpen(fromUI: number, ...args: any[]): void {
        let progress = args[0].progress as number;
        this.progressLab.string = `<b>${LocalizationMgr.ins.t("ReviveContent2", Math.floor(progress * 100) + "%")}`;
        this.reviveBtn.on("click", this.onClickRevive, this);
        this.closeBtn.on("click", this.onClickClose, this);
        TrackLogMgr.addLog("show_revive");
        audioManager.instance.playSound("time_out");
    }

    private onClickRevive() {
        if (this.clicked) return;
        this.clicked = true;
        GameMgr.ins.onRevive();
        TrackLogMgr.addLog("revive_page_click", { type: 'revive' });
        this.onBtn_close();
    }

    private onClickClose() {
        if (this.clicked) return;
        this.clicked = true;

        GameMgr.ins.startGame();
        TrackLogMgr.addLog("revive_page_click", { type: 'close' });
        this.onBtn_close();
    }
}
