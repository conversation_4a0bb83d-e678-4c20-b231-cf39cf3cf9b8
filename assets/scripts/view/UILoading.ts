import { UIView } from "../../scripts/frame/ui/UIView";
import { audioManager } from "../frame/manager/audioManager";


const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('UI/UILoading')
export default class UILoading extends UIView {

    @property(cc.ProgressBar) progressbar: cc.ProgressBar = null;
    @property(cc.Label) progressLab: cc.Label = null;

    public onOpen(fromUI: number, ...args): void {
        this.progressbar.progress = 0;
    }

    protected update(dt: number): void {

        this.progressLab.string = `${Math.floor(this.progressbar.progress * 100)}%`;

    }

    updateProgress(toProgress: number, duration: number, callBack: Function = null) {
        cc.Tween.stopAllByTarget(this.progressbar);
        cc.tween(this.progressbar).to(duration, { progress: toProgress }).call(() => {
            if (callBack) callBack();
            if (this.progressbar.progress >= 1) {
                this.close();
            }
        }).start();
    }

    close() {
        this.node.active = false;
        this.onBtn_close();
    }

}
