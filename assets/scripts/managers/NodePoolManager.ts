
const { ccclass, property } = cc._decorator;

export enum PoolType {
    PinItem,
    Pole,
}

@ccclass
export default class NodePoolManager extends cc.Component {
    @property(cc.Prefab) prefabs: cc.Prefab[] = [];
    private static _ins: NodePoolManager;
    public static get ins() {
        return this._ins;
    }
    private poolMap: Map<PoolType, cc.NodePool> = new Map();

    onLoad() {
        NodePoolManager._ins = this;
        for (let typ = 0; typ < this.prefabs.length; typ++) {
            let nodePool = this.getOrCreatPool(typ);
            let prefab = this.prefabs[typ];
            for (let index = 0; index < 5; index++) {
                let node = cc.instantiate(prefab)
                nodePool.put(node)
            }
        }
    }

    private getOrCreatPool(typ: PoolType) {
        let pool = this.poolMap.get(typ);
        if (!pool) {
            pool = new cc.NodePool();
            this.poolMap.set(typ, pool);
        }
        return pool;
    }

    put(typ: PoolType, node: cc.Node) {
        if (node != undefined) {
            this.getOrCreatPool(typ).put(node);
        }
    }

    get(typ: PoolType) {
        let n = this.getOrCreatPool(typ).get();
        if (!n) {
            n = cc.instantiate(this.prefabs[typ]);
        }
        return n;
    }

    clearPool(typ: PoolType) {
        let pool = this.poolMap.get(typ);
        if (pool) pool.clear();
    }

}