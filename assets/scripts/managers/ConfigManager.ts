import { BaseModel } from "../frame/model/BaseModel";
import { LoaderTools } from "../frame/utils/LoaderTools";
import { GuideConfigItem, LevelConfigItem } from "../model/config/GuideConfig";
import { TexFillConfigItem } from "../model/config/TexFillConfig";

export class ConfigManager extends BaseModel {

    private static _ins: ConfigManager;
    public static get ins(): ConfigManager { return this._ins = this._ins || new ConfigManager() }

    public GuideConfig: GuideConfigItem[] = [];
    public LevelConfig: LevelConfigItem[] = null;


    public async initConfigs(configList) {
        let configs = {};
        for (const key in configList) {
            let url = configList[key];
            let config = await LoaderTools.loadRes<cc.JsonAsset>('config', url);
            if (config) {
                configs[key] = config.json;
            }
        }
        this.initAllConfigData(configs);
    }

    initAllConfigData(data: any) {
        this.injection(data);
    }

    getGuideConfigItem(guideId: number) {
        return this.GuideConfig.find(x => x.GuideID == guideId);
    }

    private texFillCofigs: TexFillConfigItem[] = [];
    async getTexFillConfigItem(level: number): Promise<TexFillConfigItem> {
        let item = this.texFillCofigs[level];
        if (item) return item;
        item = (await LoaderTools.loadRes<cc.JsonAsset>("config", "texFill/" + level))?.json as TexFillConfigItem;
        this.texFillCofigs[level] = item;
        return item;
    }
}