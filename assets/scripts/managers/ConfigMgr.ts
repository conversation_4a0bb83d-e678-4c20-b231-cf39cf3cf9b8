import { LoaderTools } from "../frame/utils/LoaderTools";
import BlockItem from "../game/BlockItem";
import LayerItem from "../game/LayerItem";
import PinItem from "../game/PinItem";
import { TexFillConfigItem } from "../model/config/TexFillConfig";
import { GameModel } from "../model/GameModel";
import { BlockItemConfig, LayerConfig, LevelConfig, PinColor, PinItemConfig } from "../model/LevelConfig";
import { ModelMgr } from "../model/ModelMgr";
import { ConfigManager } from "./ConfigManager";
import GameMgr from "./GameMgr";


const { ccclass, property } = cc._decorator;


@ccclass
export default class ConfigMgr extends cc.Component {

    @property
    private projectPath: string = "/Users/<USER>/documents/proj/Demo/assets/";
    @property
    private configPath: string = "Bundles/Configs/";
    @property(cc.Button)
    private exportBtn: cc.Button = null;
    @property(cc.Button)
    private importBtn: cc.Button = null;
    _level: number = 1;
    @property()
    get level() {
        return this._level;
    }
    set level(v) {
        if (!v) return;
        this._level = v;
        this.loadingLevelInEditor();
    }

    @property
    exportLevel : number = 1;
    @property
    poleCount: number = 0;
    @property
    tsaSize: number = 0;

    @property(cc.RichText)
    pinCountLabel: cc.RichText = null;

    pinCount: number = 0;


    layerNodes: cc.Node[] = []; // 每一层的节点

    @property(cc.Button)
    private updateBtn: cc.Button = null;
    @property()
    minLayerNumber: number = 1;
    @property()
    maxLayerNumber: number = 10;

    @property(cc.Node)
    root: cc.Node = null;
    pinPrefab: cc.Prefab = null;

    levelConfig: LevelConfig = null;

    private static _ins: ConfigMgr;
    public static get ins() {
        return this._ins;
    }

    onLoad() {
        ConfigMgr._ins = this;
    }

    protected async start() {
        this.exportBtn.node.on(cc.Node.EventType.MOUSE_DOWN, this.onExportBtnClick, this);
        this.importBtn.node.on(cc.Node.EventType.MOUSE_DOWN, this.onImportBtnClick, this);
        this.updateBtn.node.on(cc.Node.EventType.MOUSE_DOWN, this.onUpdateBtnClick, this);

        this.pinCount = 0;
        // GameMgr.ins.blockItems.forEach((blockItem) => {
        //     console.log("BlockItem: " + blockItem.name)
        //     cc.director.getPhysicsManager().enabled = false;
        //     cc.director.getCollisionManager().enabled = false;
        //     // blockItem.getComponent(BlockItem).setup();
        //     // cc.director.getPhysicsManager().enabled = true;
        //     // cc.director.getCollisionManager().enabled = true;

        //     blockItem.children.forEach((child) => {
        //         this.pinCount++;
        //     });
        // });
        let levelConf = new LevelConfig();
        this.levelConfig = levelConf;
        levelConf.layers = [];
        this.layerNodes.forEach(node => {
            let layerConf = new LayerConfig();
            layerConf.x = parseFloat(node.getPosition().x.toFixed(3));
            layerConf.y = parseFloat(node.getPosition().y.toFixed(3));
            layerConf.idx = node.getComponent(LayerItem).layerIndex;
            layerConf.blocks = node.getComponent(LayerItem).getBlockConfigs();
            levelConf.layers.push(layerConf);
        });
        levelConf.level = this._level;
        levelConf.poleCount = this.poleCount;
        levelConf.tsaCount = this.tsaSize;

        this.root.children.forEach((child) => {
            child.destroy();
        });

        if (this.pinPrefab == null)
            this.pinPrefab = await LoaderTools.loadRes<cc.Prefab>("game", "prefab/pin");

        // 加载所有shape资源
        let shapes: Array<number> = [];
        levelConf.layers.forEach((layerData) => {
            layerData.blocks.forEach((blockData) => {
                shapes.push(blockData.shape);
            });
        });
        for (let i = 0; i < shapes.length; i++) {
            let prefab = await GameMgr.ins.loadPrefabByShape(shapes[i]);
        }
        cc.director.getPhysicsManager().enabled = false;
        for (let i = 0; i < levelConf.layers.length; i++) {
            let layerData = levelConf.layers[i];
            let layerNode = new cc.Node("Layer" + layerData.idx);
            layerNode.parent = this.root;
            layerNode.setPosition(layerData.x, layerData.y);
            layerNode.addComponent(LayerItem).layerIndex = layerData.idx;
            layerData.blocks.forEach((blockData) => {
                let block = cc.instantiate(GameMgr.ins.blockPrefabs.get(blockData.shape));
                block.parent = layerNode;
                block.setPosition(blockData.x, blockData.y);
                block.angle = blockData.rotation;
                block.getComponent(BlockItem).realWidth = blockData.width;
                block.getComponent(BlockItem).realHeight = blockData.height;
                let groupIndex = layerData.idx % 8;
                if (layerData.idx >= 8) groupIndex++;
                block.groupIndex = groupIndex;
                for (let j = 0; j < blockData.pins.length; j++) {
                    let pinData = blockData.pins[j];

                    let pin = cc.instantiate(this.pinPrefab);
                    pin.groupIndex = groupIndex;
                    pin.angle = -block.angle;
                    pin.parent = block;
                    pin.setPosition(pinData.x, pinData.y);
                    pin.width = pinData.width;
                    pin.height = pinData.height;
                    pin.getComponent(PinItem).realLayer = layerData.idx;
                    this.pinCount++;
                }
                block.getComponent(BlockItem).realLayer = layerData.idx;
                block.getComponent(BlockItem).setup();
                if(block.children.length == 1) block.getComponent(BlockItem).check();
                cc.director.getPhysicsManager().enabled = true;
                cc.director.getCollisionManager().enabled = true;
            });
        }

        this.updatePinCountDisplay();
    }

    async updatePinCountDisplay() {
        let config: TexFillConfigItem = null;
        if (CC_EDITOR)
            config = (await this.loadEditorRes<cc.JsonAsset>("bundles/config/texFill/" + this._level + ".json")).json as TexFillConfigItem;
        else
            config = await ConfigManager.ins.getTexFillConfigItem(this.level);
        this.pinCountLabel.string = "<color=#ff0000>Total Pin Count: " + this.pinCount + " fit pin count is " + config.shapes.length * 3 + "</color>";
    }

    private onExportBtnClick() {
        console.log("export config");

        // let levelConf = new LevelConfig();
        // levelConf.layers = [];
        // this.layerNodes.forEach(node => {
        //     let layerConf = new LayerConfig();
        //     layerConf.x = parseFloat(node.getPosition().x.toFixed(3));
        //     layerConf.y = parseFloat(node.getPosition().y.toFixed(3));
        //     layerConf.idx = node.getComponent(LayerItem).layerIndex;
        //     layerConf.blocks = node.getComponent(LayerItem).getBlockConfigs();
        //     levelConf.layers.push(layerConf);
        // });
        // levelConf.level = this._level;
        // levelConf.poleCount = this.poleCount;
        // levelConf.tsaCount = this.tsaSize;

        console.log("layer count: " + this.levelConfig.layers.length);

        let content = JSON.stringify(this.levelConfig);
        jsb.fileUtils.writeStringToFile(content, `${this.projectPath}${this.configPath}${this.exportLevel}.json`);
    }

    private async onImportBtnClick() {
        console.log("import config");
        let levelConf = (await LoaderTools.loadRes<cc.JsonAsset>("config", "level/" + this._level)).json as LevelConfig;
        if (this.pinPrefab == null)
            this.pinPrefab = await LoaderTools.loadRes<cc.Prefab>("game", "prefab/pin");

        // 加载所有shape资源
        let shapes: Array<number> = [];
        levelConf.layers.forEach((layerData) => {
            layerData.blocks.forEach((blockData) => {
                shapes.push(blockData.shape);
            });
        });
        for (let i = 0; i < shapes.length; i++) {
            let prefab = await GameMgr.ins.loadPrefabByShape(shapes[i]);
        }
        cc.director.getPhysicsManager().enabled = false;
        for (let i = 0; i < levelConf.layers.length; i++) {
            let layerData = levelConf.layers[i];
            let layerNode = new cc.Node("Layer" + layerData.idx);
            layerNode.parent = this.root;
            layerNode.setPosition(layerData.x, layerData.y);
            layerNode.addComponent(LayerItem).layerIndex = layerData.idx;
            layerData.blocks.forEach((blockData) => {
                let block = cc.instantiate(GameMgr.ins.blockPrefabs.get(blockData.shape));
                block.parent = layerNode;
                block.setPosition(blockData.x, blockData.y);
                block.angle = blockData.rotation;
                block.width = blockData.width;
                block.height = blockData.height;
                let groupIndex = layerData.idx % 8;
                if (layerData.idx >= 8) groupIndex++;
                block.groupIndex = groupIndex;
                for (let j = 0; j < blockData.pins.length; j++) {
                    let pinData = blockData.pins[j];
                    let pin = cc.instantiate(this.pinPrefab);
                    pin.groupIndex = groupIndex;
                    pin.parent = block;
                    pin.setPosition(pinData.x, pinData.y);
                    pin.width = pinData.width;
                    pin.height = pinData.height;
                    this.pinCount++;
                }
            });
        }
        this.updatePinCountDisplay();
    }

    async onUpdateBtnClick() {
        let levelConf = (await LoaderTools.loadRes<cc.JsonAsset>("config", "level/" + this._level)).json as LevelConfig;
        if (this.pinPrefab == null)
            this.pinPrefab = await LoaderTools.loadRes<cc.Prefab>("game", "prefab/pin");

        // 加载所有shape资源
        let shapes: Array<number> = [];
        levelConf.layers.forEach((layerData) => {
            layerData.blocks.forEach((blockData) => {
                shapes.push(blockData.shape);
            });
        });
        for (let i = 0; i < shapes.length; i++) {
            let prefab = await GameMgr.ins.loadPrefabByShape(shapes[i]);
        }
        cc.director.getPhysicsManager().enabled = false;
        if (this.maxLayerNumber > levelConf.layers.length) throw new Error("max layer number is invalid");
        if (this.minLayerNumber < 1 || this.minLayerNumber > levelConf.layers.length) throw new Error("min layer number is invalid");
        for (let i = 0; i < this.layerNodes.length; i++) {
            let layerItem = this.layerNodes[i].getComponent(LayerItem);
            if (layerItem.layerIndex >= this.minLayerNumber && layerItem.layerIndex <= this.maxLayerNumber) {
                levelConf.layers.forEach((layerData) => {
                    if (layerData.idx == layerItem.layerIndex) {
                        layerData.x = parseFloat(layerItem.node.getPosition().x.toFixed(3));
                        layerData.y = parseFloat(layerItem.node.getPosition().y.toFixed(3));
                        layerData.blocks = layerItem.getBlockConfigs();
                    }
                });
            }
        }
        let content = JSON.stringify(levelConf);
        jsb.fileUtils.writeStringToFile(content, `${this.projectPath}${this.configPath}${this._level}.json`);
        console.log("Updata config success");
    }


    shapes = new Map<number, cc.Prefab>();
    private async loadingLevelInEditor() {
        this.root.children.forEach(node => node.destroy());
        this.pinCount = 0;
        let levelConf = (await this.loadEditorRes<cc.JsonAsset>("bundles/config/level/" + this._level + ".json")).json as LevelConfig;
        if (this.pinPrefab == null)
            this.pinPrefab = await this.loadEditorRes<cc.Prefab>("bundles/game/prefab/pin.prefab");

        // 加载所有shape资源
        let shapes: Array<number> = [];
        levelConf.layers.forEach((layerData) => {
            layerData.blocks.forEach((blockData) => {
                shapes.push(blockData.shape);
            });
        });
        for (let i = 0; i < shapes.length; i++) {
            let prefab = await this.loadEditorRes<cc.Prefab>("bundles/game/prefab/block" + shapes[i] + ".prefab");
            if (!this.shapes.has(shapes[i])) {
                this.shapes.set(shapes[i], prefab);
            }
        }
        cc.director.getPhysicsManager().enabled = false;
        for (let i = 0; i < levelConf.layers.length; i++) {
            let layerData = levelConf.layers[i];
            let layerNode = new cc.Node("layer" + layerData.idx);
            layerNode.parent = this.root;
            layerNode.setPosition(layerData.x, layerData.y);
            layerNode.addComponent(LayerItem).layerIndex = layerData.idx;
            layerData.blocks.forEach((blockData) => {
                let block = cc.instantiate(this.shapes.get(blockData.shape));
                block.setParent(layerNode);
                block.setPosition(blockData.x, blockData.y);
                block.angle = blockData.rotation;
                block.width = blockData.width;
                block.height = blockData.height;
                let groupIndex = layerData.idx % 8;
                if (layerData.idx >= 8) groupIndex++;
                block.groupIndex = groupIndex;
                for (let j = 0; j < blockData.pins.length; j++) {
                    let pinData = blockData.pins[j];

                    let pin = cc.instantiate(this.pinPrefab);
                    pin.groupIndex = groupIndex;
                    pin.parent = block;
                    pin.setPosition(pinData.x, pinData.y);
                    pin.width = pinData.width;
                    pin.height = pinData.height;
                    pin.angle = -blockData.rotation;
                    this.pinCount++;
                }
            });
        }
        this.updatePinCountDisplay();
    }

    async loadEditorRes<T extends cc.Asset>(path: string): Promise<T> {
        return new Promise<T>((resolve) => {
            var uuid = Editor.assetdb.remote.urlToUuid("db://assets/" + path);
            if (uuid) {
                cc.assetManager.loadAny(uuid, function (err, asset: T) {
                    if (err) {
                        Editor.error("load err: " + path);
                        resolve(null);
                    } else {
                        resolve(asset);
                    }

                });
            } else {
                Editor.error(path + " not found");
                resolve(null);
            }
        });
    }


}
