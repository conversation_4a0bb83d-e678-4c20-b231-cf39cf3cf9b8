import App from "../frame/app/App";
import { LogMgr } from "../frame/app/LogMgr";
import { ModelMgr } from "../model/ModelMgr";

export class TrackLogMgr {

    public static async addLog(name, data: object = {}) {
        if (!data["ver"]) {
            data["ver"] = App.device.version
        }
        if (!data["level"]) {
            data["level"] = ModelMgr.ins.game.level;
        }

        LogMgr.addLog(name, data);
    }
}