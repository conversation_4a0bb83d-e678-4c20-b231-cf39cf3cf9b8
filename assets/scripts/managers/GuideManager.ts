import { LocalizationMgr } from "../frame/localization/LocalizationMgr";
import { uiManager } from "../frame/ui/UIManager";
import { LocalStorageMgr } from "../frame/utils/LocalStorageMgr";
import { UIID_MAIN } from "../GameConst";
import { GameData } from "../model/GameModel";
import { ModelMgr } from "../model/ModelMgr";
import { ConfigManager } from "./ConfigManager";
import GameMgr from "./GameMgr";
import { TrackLogMgr } from "./TrackLogMgr";

export class GuideData {
    guideSubStep: number = null;
    spinStep: number = null;
    target1: cc.Node = null;
    target2: cc.Node = null;
    tips: string = "";
    needOpenAni: boolean = true;
    tipsPos: cc.Vec2 = null;
    maskOpc: number = 140;
    closeCb: Function = null;
    isFullClick: boolean = false;
    isHandInMiddle: boolean = false;
    isOnlyShow: boolean = false;
    useFinger: number = 1;
}

export enum GuideStep {
    NotStarted = 1000,
    NewGame,
    NewGame2,
    NewGame3,
    NewGame4,
}

export class GuideManager {
    private static _ins: GuideManager;
    public static get ins(): GuideManager { return this._ins = this._ins || new GuideManager(); }

    lastNewGameGuideStep: number = 0;
    constructor() {
        this.lastNewGameGuideStep = LocalStorageMgr.getValue("NewGameGuideSubStep", 0);
        // this.lastNewGameGuideStep = GuideStep.NotStarted;
    }

    triggerNewGameGuide(gameUI: cc.Node) {
        if (this.lastNewGameGuideStep < GuideStep.NewGame) {
            this.triggerNewGame1(gameUI);
        } else if (this.lastNewGameGuideStep < GuideStep.NewGame2) {
            this.triggerNewGame2(gameUI);
        } else if (this.lastNewGameGuideStep < GuideStep.NewGame3) {
            this.triggerNewGame3(gameUI);
        } else if (this.lastNewGameGuideStep < GuideStep.NewGame4) {
            this.triggerNewGame4(gameUI);
        }
    }

    triggerNewGame1(gameUI: cc.Node) {
        let guideData = new GuideData();
        guideData.guideSubStep = GuideStep.NewGame;
        guideData.target1 = cc.find("pen/cmpParent", gameUI)?.children[0];
        guideData.needOpenAni = true;
        guideData.tips = LocalizationMgr.ins.t(ConfigManager.ins.getGuideConfigItem(guideData.guideSubStep)?.LanguageKey);
        guideData.closeCb = () => {
            this.triggerNewGame2(gameUI);
        }
        uiManager.open(UIID_MAIN.UIGuide, guideData);
        this.lastNewGameGuideStep = guideData.guideSubStep;
        this.saveNewGameGuideStep();
        TrackLogMgr.addLog("guide", { guide_id: guideData.guideSubStep });
    }

    triggerNewGame2(gameUI: cc.Node) {
        let guideData = new GuideData();
        guideData.guideSubStep = GuideStep.NewGame2;
        guideData.target1 = GameData.getFirstBlock();
        guideData.needOpenAni = true;
        guideData.tips = LocalizationMgr.ins.t(ConfigManager.ins.getGuideConfigItem(guideData.guideSubStep)?.LanguageKey);
        guideData.closeCb = () => {
            this.triggerNewGame3(gameUI);
        }
        uiManager.open(UIID_MAIN.UIGuide, guideData);
        this.lastNewGameGuideStep = guideData.guideSubStep;
        this.saveNewGameGuideStep();
        TrackLogMgr.addLog("guide", { guide_id: guideData.guideSubStep });
    }

    triggerNewGame3(gameUI: cc.Node) {
        let guideData = new GuideData();
        guideData.guideSubStep = GuideStep.NewGame3;
        guideData.target1 = cc.find("pen/tsaParent", gameUI);;
        guideData.needOpenAni = true;
        guideData.tips = LocalizationMgr.ins.t(ConfigManager.ins.getGuideConfigItem(guideData.guideSubStep)?.LanguageKey);
        guideData.closeCb = () => {
            this.triggerNewGame4(gameUI);
        }
        uiManager.open(UIID_MAIN.UIGuide, guideData);
        this.lastNewGameGuideStep = guideData.guideSubStep;
        this.saveNewGameGuideStep();
        TrackLogMgr.addLog("guide", { guide_id: guideData.guideSubStep });
    }

    triggerNewGame4(gameUI: cc.Node) {
        let guideData = new GuideData();
        guideData.guideSubStep = GuideStep.NewGame4;
        guideData.target1 = GameData.getFirstBlock();
        guideData.needOpenAni = true;
        guideData.tips = LocalizationMgr.ins.t(ConfigManager.ins.getGuideConfigItem(guideData.guideSubStep)?.LanguageKey);
        uiManager.open(UIID_MAIN.UIGuide, guideData);
        this.lastNewGameGuideStep = guideData.guideSubStep;
        this.saveNewGameGuideStep();
        TrackLogMgr.addLog("guide", { guide_id: guideData.guideSubStep });
    }

    private saveNewGameGuideStep() {
        LocalStorageMgr.setValue("NewGameGuideSubStep", this.lastNewGameGuideStep);
    }
}