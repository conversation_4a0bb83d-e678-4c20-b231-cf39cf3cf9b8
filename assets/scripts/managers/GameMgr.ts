import TextureFill from "../colorfill/TextureFill";
import App from "../frame/app/App";
import { LocalizationMgr } from "../frame/localization/LocalizationMgr";
import { audioManager } from "../frame/manager/audioManager";
import { uiManager } from "../frame/ui/UIManager";
import { AdapationUtil } from "../frame/utils/AdaptionUtil";
import { LoaderTools } from "../frame/utils/LoaderTools";
import { RandomUtil } from "../frame/utils/RandomUtil";
import Utils from "../frame/utils/Utils";
import BlockItem from "../game/BlockItem";
import PinItem from "../game/PinItem";
import { UIID_MAIN } from "../GameConst";
import { LevelConfigItem } from "../model/config/GuideConfig";
import { TexFillConfigItem } from "../model/config/TexFillConfig";
import { GameData } from "../model/GameModel";
import { LevelConfig } from "../model/LevelConfig";
import { ModelMgr } from "../model/ModelMgr";
import { ConfigManager } from "./ConfigManager";
import { GuideManager } from "./GuideManager";
import { TrackLogMgr } from "./TrackLogMgr";


const { ccclass, property } = cc._decorator;

@ccclass
export default class GameMgr extends cc.Component {
    @property(cc.Prefab) gameUIPrfab: cc.Prefab = null;
    @property circleFregmentCount: number = 8;
    @property circleRadius: number = 10;
    @property displayLayerCount: number = 2;

    root: cc.Node = null;
    gameNode: cc.Node;
    colorImages: Map<number, cc.SpriteFrame> = new Map(); // 前面是颜色，后面是资源
    lockImage: cc.SpriteFrame = null;
    penEmptyImage: cc.SpriteFrame = null;
    penImages: Map<number, cc.SpriteFrame> = new Map(); // 前面是颜色，后面是资源
    penFullImages: Map<number, cc.SpriteFrame> = new Map(); // 前面是颜色，后面是资源
    tsaParent: cc.Node = null;
    cmpParent: cc.Node = null;
    pinAnimNode: cc.Node = null;
    penAnimNode: cc.Node = null;
    private startGameTime: number = 0;
    blockPrefabs: Map<number, cc.Prefab> = new Map();
    pinPrefab: cc.Prefab = null;
    holeImage: cc.SpriteFrame = null;
    blockItemBuffer: Array<BlockItem> = [];

    private static _ins: GameMgr = null;
    public static get ins() {
        return this._ins;
    }

    async preloadAssets() {
        let level = ModelMgr.ins.game.level;
        let levelConf = (await LoaderTools.loadRes<cc.JsonAsset>("config", "level/" + level)).json as LevelConfig;
        GameData.levelConfig = levelConf;
        await this.loadAssets();
    }

    async loadLevel() {
        await GameMgr.ins.initLevel(GameData.levelConfig);
    }

    async loadAssets() {
        for (let i = 1; i <= 9; i++) {
            if (!this.colorImages.has(i)) {
                let colorImage = await LoaderTools.loadSpriteFrame("game", "texture/altas/paint_0" + i);
                this.colorImages.set(i, colorImage);
            }

            if (!this.penImages.has(i)) {
                let penImage = await LoaderTools.loadSpriteFrame("game", "texture/altas/pen_0" + i);
                this.penImages.set(i, penImage);
                let penFullImage = await LoaderTools.loadSpriteFrame("game", "texture/altas/pen_0" + i + "full");
                this.penFullImages.set(i, penFullImage);
            }
        }
        if (this.lockImage == null) {
            this.lockImage = await LoaderTools.loadSpriteFrame("game", "texture/altas/lock_pen");
        }
        if (this.penEmptyImage == null) {
            this.penEmptyImage = await LoaderTools.loadSpriteFrame("game", "texture/altas/pen_s_empty");
        }
        if (this.holeImage == null) {
            this.holeImage = await LoaderTools.loadSpriteFrame("game", "texture/hole");
        }

        // 加载所有shape资源
        let shapes: Array<number> = [];
        GameData.levelConfig.layers.forEach((layerData) => {
            layerData.blocks.forEach((blockData) => {
                shapes.push(blockData.shape);
            });
        });
        for (let i = 0; i < shapes.length; i++) {
            let prefab = await this.loadPrefabByShape(shapes[i]);
        }
    }

    protected onLoad() {
        GameMgr._ins = this;
        cc.director.getPhysicsManager().enabled = true;
        cc.director.getCollisionManager().enabled = true;
        // cc.director.getPhysicsManager().debugDrawFlags = 1;  
        ModelMgr.ins.game.level = 1;

        // TEST BLOCK TODO：REMOVE
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onKeyDown(event) {
        if (event.keyCode === cc.macro.KEY.space) {
            console.log("空格键被按下！");
            // this.cleanTsa();             // 清理道具
            // GameData.enableHammer();
        }
    }

    async startGame() {
        this.startGameTime = Date.now();
        let level = ModelMgr.ins.game.level;
        if (!this.root) {
            let gameNode = cc.instantiate(this.gameUIPrfab);
            gameNode.setParent(cc.find("Canvas"));
            gameNode.setPosition(0, 0);
            gameNode.setSiblingIndex(1);
            let node = new cc.Node("GameMainArea");
            node.parent = gameNode;
            node.setPosition(0, 0);
            this.tsaParent = cc.find("pen/tsaParent", gameNode);
            this.cmpParent = cc.find("pen/cmpParent", gameNode);
            gameNode.getChildByName("remove").active = false;
            GameData.textureFill = cc.find("frame/mask/TextureFill", gameNode).getComponent(TextureFill);
            this.root = node;
            this.gameNode = gameNode;
            TrackLogMgr.addLog("show_main");
            let lvGmBtn = cc.find("frame/level/levelBtn", gameNode);
            lvGmBtn.active = App.device.IsTestPack();
            lvGmBtn.on("click", async () => {
                ModelMgr.ins.game.passLevel();
                await this.preloadAssets();
                this.startGame();
            }, this);
            cc.find("frame", gameNode).getComponent(cc.Widget).top = AdapationUtil.getCustomAdaption(30, 107);
            cc.find("pen", gameNode).scale = AdapationUtil.getCustomAdaption(0.8, 1);
            cc.find("privacy", gameNode).on("click", () => {
                cc.sys.openURL(App.info.PrivacyPolicyUrl);
            }, this);
            cc.find("termOfUse", gameNode).on("click", () => {
                cc.sys.openURL(App.info.TermsOfServiceUrl);
            }, this);
        }
        cc.find("frame/level/levelLab", this.gameNode).getComponent(cc.Label).string = LocalizationMgr.ins.t("LevelN", level);
        let pen = cc.find("frame/pen", this.gameNode);
        TrackLogMgr.addLog("level_start", { level: level });
        await GameData.textureFill.init(level, pen);
        await GameMgr.ins.loadLevel(); // 加载游戏的
        this.gameNode.getChildByName("remove").active = true;
        GuideManager.ins.triggerNewGameGuide(this.gameNode);
    }

    reset() {
        GameData.reset();
        this.fillColorMap = new Map();
        this.tsaParent.children.forEach((node, index) => {
            node.active = false;
        });
        this.cmpParent.children.forEach((node) => {
            // node.stopAllActions();
            // node.children[1].stopAllActions();
            cc.Tween.stopAllByTarget(node);
            cc.Tween.stopAllByTarget(node.children[1]);
            node.active = true;
            node.children[0].active = true;
            node.getComponent(cc.Sprite).spriteFrame = this.penEmptyImage;
            node.children[1].getComponent(cc.Sprite).fillRange = 0;
        });
        this.blockItemBuffer = [];
    }

    async initLevel(levelConfig: LevelConfig) {
        this.reset();
        if (this.pinAnimNode == null || this.penAnimNode == null) {
            this.pinAnimNode = new cc.Node("PinAnimNode");
            this.pinAnimNode.setParent(this.root.parent);
            this.pinAnimNode.setPosition(0, 0);
            this.pinAnimNode.addComponent(cc.Sprite).spriteFrame = this.colorImages.get(1);
            this.pinAnimNode.active = false;
            this.pinAnimNode.setAnchorPoint(0.5, 0.35);

            this.penAnimNode = new cc.Node("PenAnimNode");
            this.penAnimNode.setParent(this.root.parent);
            this.penAnimNode.setPosition(0, 0);
            this.penAnimNode.addComponent(cc.Sprite).spriteFrame = this.penEmptyImage;
            this.penAnimNode.active = false;
        }
        GameData.levelConfig = levelConfig;
        cc.director.getPhysicsManager().enabled = false;

        if (this.pinPrefab == null)
            this.pinPrefab = await LoaderTools.loadRes<cc.Prefab>("game", "prefab/pin");

        // 加载最大显示的层
        for (let i = 0; i < GameData.levelConfig.layers.length; i++) {
            this.loadOneLayerData();
        }
        // 显示第一个轮廓
        if (GameData.levelBlocks.size >= this.displayLayerCount + 1) {
            GameData.levelBlocks.get(this.displayLayerCount + 1).forEach((block) => {
                block.active = true;
                block.opacity = 1;
                block.children.forEach((pin) => {
                    pin.active = false;
                });
            });
        }
        cc.director.getPhysicsManager().enabled = true;
        GameData.init();
        this.dispatchColor(); // 设置pin的颜色，根据当前显示的前四层来分配pin的颜色
        for (let i = 0; i < GameData.poleCount; i++) {
            console.log("box id: " + i);
            let color = GameData.getColorByPercentage();
            if (GameData.colorAvilable(color)) {
                this.initCmpColor(i, color);
            }
        }
        GameData.currentLayer = this.displayLayerCount + 1;
    }

    initCmpColor(i, color) {
        GameData.compareColors[i] = color;
        GameData.cmpColorCount[i] = 3;
        this.cmpParent.children[i].getComponent(cc.Sprite).spriteFrame = this.penImages.get(color);
        this.cmpParent.children[i].children[0].active = false;
        let fill = this.cmpParent.children[i].children[1].getComponent(cc.Sprite);
        fill.fillRange = 1 - GameData.cmpColorCount[i] / 3;
        fill.spriteFrame = this.penFullImages.get(color);
    }
    updateCmpColor(i, pin, color) { // 更新只有一种情况，就是数量减少
        GameData.cmpColorCount[i]--;
        let fillRange = 1 - GameData.cmpColorCount[i] / 3;
        this.playPinClickAudio();
        this.playCollectPinAnim(color, pin, this.cmpParent.children[i], () => {
            cc.tween(this.cmpParent.children[i].children[1].getComponent(cc.Sprite)).to(0.1, { fillRange: fillRange }).call(() => {
                // this.playPinClickAudio();
                // TODO: 播放其他的collect音效
            }).start();
        });

    }
    checkTsa() {
        for (let j = 0; j < GameData.tsaSize; j++) {
            if (GameData.tsa[j] != 0 && this.collectColor(GameData.tsa[j], this.tsaParent.children[j], true)) {
                this.hideTsaItem(j);
            }
        }
        // 检查invisibleTsa
        for (let j = 0; j < GameData.invisibleTsa.length; j++) {
            if (GameData.invisibleTsa[j] != 0 && this.collectColor(GameData.invisibleTsa[j], this.tsaParent.children[0], true)) {
                GameData.invisibleTsa.splice(j, 1);
                j--;
            }
        }
    }
    loadOneLayerData() {
        GameData.curLayerCount++;
        let i = GameData.curLayerCount;
        let blockColor = GameData.getBlockColorByRandom();
        let layerData = GameData.levelConfig.layers[i - 1];
        let layerNode = new cc.Node("Layer" + layerData.idx);
        layerNode.parent = this.root;
        layerNode.setPosition(layerData.x, layerData.y);
        layerData.blocks.forEach((blockData) => {
            let block = cc.instantiate(this.blockPrefabs.get(blockData.shape));
            block.parent = layerNode;
            block.setPosition(blockData.x, blockData.y);
            block.angle = blockData.rotation;
            let blockItem = block.getComponent(BlockItem);
            blockItem.realWidth = blockData.width;
            blockItem.realHeight = blockData.height;
            blockItem.blockColor = blockColor;
            block.color = blockColor;
            let groupIndex = layerData.idx % 8;
            if (layerData.idx >= 8) groupIndex++;
            block.groupIndex = groupIndex;
            for (let j = 0; j < blockData.pins.length; j++) {
                let pinData = blockData.pins[j];
                let pin = cc.instantiate(this.pinPrefab);
                pin.groupIndex = groupIndex;
                pin.angle = -block.angle;
                pin.parent = block;
                pin.setPosition(pinData.x, pinData.y);
                pin.width = pinData.width;
                pin.height = pinData.height;
                pin.getComponent(PinItem).realLayer = layerData.idx;
                let holeNode = new cc.Node("hole");
                holeNode.parent = block;
                holeNode.setSiblingIndex(0);
                holeNode.setPosition(pinData.x, pinData.y);
                holeNode.addComponent(cc.Sprite).spriteFrame = this.holeImage;
                GameData.totalPinCount++;
            }
            blockItem.realLayer = layerData.idx;
            blockItem.setup();
            if (!GameData.levelBlocks.has(layerData.idx)) {
                GameData.levelBlocks.set(layerData.idx, [block]);
            } else {
                GameData.levelBlocks.get(layerData.idx).push(block);
            }
            if (layerData.idx > this.displayLayerCount) {
                let resetColor = blockItem.blockColor;
                let idx = GameData.layerUsedColors.indexOf(resetColor);
                if (idx >= 0)
                    GameData.layerUsedColors.splice(idx, 1);
                block.active = false;
            }
            else {
                blockItem.check();
            }
        });
        GameData.sortBlock(layerData.idx);
    }

    collectNoCoverPin(noCover, pin) {
        if (!GameData.hasCovered(pin)) {
            let pinItem = pin.getComponent(PinItem);
            if (noCover.has(pinItem.pinColor))
                noCover.set(pinItem.pinColor, noCover.get(pinItem.pinColor) + 1);
            else
                noCover.set(pinItem.pinColor, 1);
        }
    }
    dispatchColor() {
        // 收集所有的pin，然后随机打乱设置颜色
        let pins: Array<cc.Node> = new Array<cc.Node>();
        for (let i = 1; i <= GameData.levelBlocks.size; i++) {
            // console.log("dispatch layer: " + i);
            let blocks = GameData.levelBlocks.get(i);
            blocks.forEach((block) => {
                block.children.forEach((pin) => {
                    if (pin.getComponent(PinItem) == null) return;
                    pins.push(pin);
                });
            });
            if (i % 4 == 0) { // 每4个层会打乱一次，然后按照顺序设置颜色，顺序是定死的顺序
                this.assignColor(pins);
                pins = [];
            }
        }
        this.assignColor(pins);
    }
    assignColor(pins) {
        GameData.shuffle(pins);
        for (let i = 0; i < pins.length; i++) {
            if (GameData.totalColorsCount[GameData.curPinColorIndex] > 0) {
                pins[i].getComponent(PinItem).pinColor = GameData.totalColors[GameData.curPinColorIndex];
                pins[i].getComponent(cc.Sprite).spriteFrame = this.colorImages.get(GameData.totalColors[GameData.curPinColorIndex]);
                GameData.totalColorsCount[GameData.curPinColorIndex]--;
                if (GameData.totalColorsCount[GameData.curPinColorIndex] == 0) GameData.curPinColorIndex++;
            }
        }
    }
    protected update(dt: number): void {
        while (this.blockItemBuffer.length > 0) {
            let blockItem = this.blockItemBuffer.shift();
            blockItem.check();
        }
    }
    addLayer() {
        let color = GameData.getBlockColorByRandom();
        GameData.levelBlocks.forEach((blocks, layer) => {
            if (layer != GameData.currentLayer) return;
            blocks.forEach((block) => {
                block.active = true;
                block.color = color;
                cc.tween(block).to(0.5, { opacity: 255 }, { easing: "linear" }).start();
                let blockItem = block.getComponent(BlockItem);
                blockItem.blockColor = color;
                block.children.forEach((pin) => {
                    pin.active = true;
                });
                this.blockItemBuffer.push(blockItem);
            });
        });
        GameData.currentLayer++;
        GameData.showNextLayerOutline();
    }
    onRemoveBlock(resetColor) {
        let layerCount = 0;
        GameData.levelBlocks.forEach((blocks, layer) => {
            if (layer >= GameData.currentLayer) return;
            let visible = false;
            blocks.forEach((block) => {
                if (visible) return;
                visible = block.active;
            })
            if (!visible) return;
            layerCount++;
        });
        if (layerCount < this.displayLayerCount) {
            let idx = GameData.layerUsedColors.indexOf(resetColor);
            if (idx >= 0)
                GameData.layerUsedColors.splice(idx, 1);
            this.addLayer();
        }
    }

    async loadPrefabByShape(shape: number) {
        if (!this.blockPrefabs.has(shape)) {
            let prefab = await LoaderTools.loadRes<cc.Prefab>("game", "prefab/block" + shape);
            this.blockPrefabs.set(shape, prefab);
        }
    }

    playCollectAudio() {
        if (audioManager.instance != null)
            audioManager.instance.playSound("collect_key");
    }

    playPinClickAudio() {
        if (audioManager.instance != null) {
            audioManager.instance.vibrate(30, 160);
            audioManager.instance.playSound("click");
        }
    }

    checkGameLoseState() {
        if (this.fillColorMap.size != 0) return;
        let lose = true;
        GameData.tsa.forEach((color, index) => {
            if (lose && color != 0) return;
            lose = false;
        });
        if (lose) {
            this.gameOver();
        }
    }

    gameOver() {
        // 游戏失败
        let progress = GameData.percentage;  // 每个关卡最多有两次复活机会，复活后额外解锁一个架子
        if (GameData.poleCount < 4) {
            uiManager.open(UIID_MAIN.RevivePop, { progress: progress });
        } else {
            let duration = Date.now() - this.startGameTime;
            uiManager.open(UIID_MAIN.LosePop, { textureData: GameData.textureFill.displayData, width: GameData.textureFill.startN.width, height: GameData.textureFill.startN.height, progress: progress, duration: duration });
            GameData.textureFill?.stopAnim();
        }
        GameData.SaveLevelProgress(progress);
    }

    win() {
        let duration = Date.now() - this.startGameTime;
        uiManager.open(UIID_MAIN.WinPop, { level: ModelMgr.ins.game.level, duration: duration });
        ModelMgr.ins.game.passLevel();
    }
    onRevive() {
        GameData.reviveCount++;
        if (GameData.reviveCount <= 2) {
            this.openCmp(true);
        } else {
            this.cleanTsa();
        }
        this.checkTsa();
    }



    collectColor(color: number, pin: cc.Node, isTSAColor: boolean = false): boolean {
        if (color == 0) return false;
        let minCount = 10;
        let minIdx = -1;
        for (let i = 0; i < GameData.poleCount; i++) {
            if (GameData.compareColors[i] == color && GameData.cmpColorCount[i] > 0) { // 匹配颜色
                if (GameData.cmpColorCount[i] < minCount) {
                    minCount = GameData.cmpColorCount[i];
                    minIdx = i;
                }
            }
        }
        if (minIdx != -1) {
            if (GameData.totalColorEmpty()) { // totalColor为空的情况
                if (GameData.cmpColorCount[minIdx] == 1) { // 如果pole上面还有剩余的最后一个颜色，就消除这个颜色
                    GameData.cmpColorCount[minIdx] = 0;
                    let node = this.cmpParent.children[minIdx];
                    this.playCollectPinAnim(color, pin, node, () => {
                        cc.tween(node.children[1].getComponent(cc.Sprite)).to(0.1, { fillRange: 1 }).call(() => {
                            this.playCollectAudio();
                            node.getComponent(cc.Sprite).spriteFrame = this.penEmptyImage;
                            node.children[1].getComponent(cc.Sprite).fillRange = 0;
                            GameData.textureFill.fillShape(color);
                        }).start();
                    });
                }
                else {
                    this.updateCmpColor(minIdx, pin, color);
                }
            }
            else { // totalColor还有颜色的情况
                if (GameData.cmpColorCount[minIdx] > 1) {
                    this.updateCmpColor(minIdx, pin, color);
                }
                else {
                    GameData.cmpColorCount[minIdx] = 0;
                    this.playPinClickAudio();
                    this.playCollectPinAnim(color, pin, this.cmpParent.children[minIdx], () => {
                        let node = this.cmpParent.children[minIdx];
                        let sp = node.children[1].getComponent(cc.Sprite);
                        // node.stopAllActions();
                        // sp.node.stopAllActions();
                        cc.Tween.stopAllByTarget(node);
                        cc.Tween.stopAllByTarget(sp);
                        GameData.textureFill.fillShape(color);
                        cc.tween(sp).to(0.1, { fillRange: 1 }).call(() => {
                            this.playCollectAudio();
                            sp.fillRange = 0;
                            if (GameData.cmpColorCount[minIdx] == 0) {
                                node.getComponent(cc.Sprite).spriteFrame = this.penEmptyImage;
                                node.active = false;
                            }
                        }).start();
                    });
                }
            }
            return true;
        }
        if (isTSAColor) return false; // 如果是tsa颜色，就不让进入
        return this.findVacantPoleInTSA(color, pin);
    }


    displayTsaItem(i, color) {
        this.tsaParent.children[i].active = true;
        this.tsaParent.children[i].getComponent(cc.Sprite).spriteFrame = this.colorImages.get(color);
    }
    hideTsaItem(i) {
        this.tsaParent.children[i].active = false;
        GameData.tsa[i] = 0;
    }



    findVacantPoleInTSA(targetColor: number, pin: cc.Node): boolean {
        for (let i = 0; i < GameData.tsaSize; i++) {
            if (GameData.tsa[i] == 0) {
                GameData.tsa[i] = targetColor;
                this.playPinClickAudio();
                this.playCollectPinAnim(targetColor, pin, this.tsaParent.children[i], () => {
                    this.displayTsaItem(i, targetColor);
                    if (this.fillColorMap.size == 0) this.checkGameLoseState();
                });
                let usedCount = 0;
                for (let j = 0; j < GameData.tsaSize; j++) {
                    if (GameData.tsa[j] != 0) usedCount++;
                }
                if (usedCount == GameData.tsaSize - 1) {
                    this.showToast();
                }
                return true;
            }
        }
        return false;
    }

    showToast() {
        uiManager.tip("<b>" + LocalizationMgr.ins.t("Left1SpaceToast") + "</b>", 1);
    }
    openCmp(isRevive: boolean = false) {
        if (GameData.poleCount > 4) return;
        this.cmpParent.children[GameData.poleCount].children[0].active = false;
        let poleColor = isRevive ? GameData.getColorByTsa() : GameData.getColorByPercentage();
        if (GameData.colorAvilable(poleColor)) {
            this.cmpParent.children[GameData.poleCount].getComponent(cc.Sprite).spriteFrame = this.penImages.get(poleColor);
            this.initCmpColor(GameData.poleCount, poleColor)
        }
        else {
            throw new Error("color not avilable")
        }
        GameData.poleCount++;
        if (GameData.poleCount > 4) GameData.poleCount = 4;
    }

    // TODO:添加增加槽位的表现，逻辑就是直接tsaSize++，是否需要限制一下最大值
    addTsa() {
        if (GameData.tsaSize > 8) return;
        // 添加表现
        GameData.tsaSize++;
        if (GameData.tsaSize > 8) GameData.tsaSize = 8;
    }

    cleanTsa() {
        let hasEmpty = true;
        GameData.tsa.forEach((color, index) => {
            if(color != 0){
                hasEmpty = false;
            }
            GameData.invisibleTsa.push(color);
            this.hideTsaItem(index);
        });
        if(hasEmpty){
            uiManager.tip("<b>" + LocalizationMgr.ins.t("Prop3Toast") + "</b>");
        }
    }


    brokenBlock(block: cc.Node) {
        block.children.forEach((pin) => {
            if (!pin.active || pin.getComponent(PinItem) == null) return;
            GameData.invisibleTsa.push(pin.getComponent(PinItem).pinColor);
        });
        block.active = false;
        GameData.disablehammer();
    }



    playCollectPinAnim(color, startNode, endNode, callback, isTsa = false) {
        let startPos = this.gameNode.convertToNodeSpaceAR(startNode.convertToWorldSpaceAR(cc.v2(0, 0)));

        let tempNode = cc.instantiate(this.pinAnimNode);
        tempNode.setParent(this.pinAnimNode.parent);
        tempNode.setAnchorPoint(0.5, 0.35);
        tempNode.setPosition(startPos.x, startPos.y);
        tempNode.getComponent(cc.Sprite).spriteFrame = this.colorImages.get(color);
        tempNode.scale = 1;
        tempNode.active = true;
        let endPos = this.gameNode.convertToNodeSpaceAR(endNode.convertToWorldSpaceAR(cc.v2(0, 0)));
        let upPos = startPos.add(cc.Vec2.UP.mul(20));
        if (isTsa) {
            let downPos = endPos;
            endPos = this.gameNode.convertToNodeSpaceAR(endNode.convertToWorldSpaceAR(cc.v2(0, 20)));
            cc.tween(tempNode)
                .to(0.3, { position: upPos }, { easing: "cubicOut" })
                .to(0.2, { position: endPos }, { easing: "cubicOut" })
                .to(0.2, { position: downPos, scale: 0.75 }, { easing: "linear" })
                .call(() => {
                    callback();
                    tempNode.removeFromParent();
                    tempNode.destroy();
                    audioManager.instance.playSound("tickets");
                }).start();
        }
        else {
            cc.tween(tempNode)
                .to(0.3, { position: upPos }, { easing: "cubicOut" })
                .to(0.2, { position: endPos }, { easing: "cubicOut" }).call(() => {
                    callback();
                    tempNode.removeFromParent();
                    tempNode.destroy();
                    audioManager.instance.playSound("tickets");
                }).start();
        }
    }

    playPaintAnim(color, startPos, endPos, callback) {
        let idx = 0;
        this.penAnimNode[idx].setPosition(startPos.x, startPos.y);
        this.penAnimNode[idx].getComponent(cc.Sprite).spriteFrame = this.penFullImages.get(color);
        this.penAnimNode[idx].active = true;
        cc.tween(this.penAnimNode[idx])
            .to(0.3, { position: endPos })
            .call(() => {
                callback();
                this.penAnimNode[idx].active = false;
            }).start();
    }

    playNewColorAnim(node, color, callback) {
        let pos = this.gameNode.convertToNodeSpaceAR(node.convertToWorldSpaceAR(cc.v2(0, 0)));
        let startPos = pos.add(cc.Vec2.RIGHT.mul(-500));
        let endPos = pos;
        let penNode = cc.instantiate(this.penAnimNode);
        penNode.parent = this.penAnimNode.parent;
        penNode.setPosition(startPos.x, startPos.y);
        penNode.getComponent(cc.Sprite).spriteFrame = this.penImages.get(color);
        penNode.active = true;
        cc.tween(penNode)
            .to(0.7, { position: endPos }, { easing: "cubicOut" }) // 这里不能少于收集pin的动画+进度条动画的时间
            .call(() => {
                callback();
                penNode.destroy();
            }).start();
    }

    fillColorMap = new Map<number, number>(); // key 是idx， value是color

    completePaintColor(idx) {
        let paintedColor = this.fillColorMap.get(idx);
        for (let i = 0; i < GameData.poleCount; i++) {
            if (GameData.compareColors[i] == paintedColor && GameData.cmpColorCount[i] == 0) {
                // 获取新颜色
                if (GameData.totalColorEmpty()) break;
                this.fillColorMap.delete(idx);
                console.log("box id: " + i);
                let poleColor = GameData.getColorByPercentage();
                let colorAvilable = GameData.colorAvilable(poleColor);
                if (colorAvilable) {
                    GameData.updateCmpData(i, poleColor);
                }
                else {
                    throw new Error("color not avilable")
                }
                let node = this.cmpParent.children[i];
                let sp = node.children[1].getComponent(cc.Sprite);
                // node.stopAllActions();
                // sp.node.stopAllActions();
                cc.Tween.stopAllByTarget(node);
                cc.Tween.stopAllByTarget(sp);
                node.getComponent(cc.Sprite).spriteFrame = this.penImages.get(poleColor);
                node.children[0].active = false;
                sp.spriteFrame = this.penFullImages.get(poleColor);
                sp.fillRange = 0;
                this.playNewColorAnim(this.cmpParent.children[i], poleColor, () => {
                    this.checkTsa();
                    if (this.fillColorMap.size == 0) this.checkGameLoseState();
                    node.active = true;
                });
            }
        }
    }

}
