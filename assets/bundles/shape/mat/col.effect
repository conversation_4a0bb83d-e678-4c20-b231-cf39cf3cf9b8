CCEffect %{
techniques:
  - passes:
      - vert: vs
        frag: fs
        blendState:
          targets:
            - blend: true
        rasterizerState:
          cullMode: none
        properties:
          texture: { value: white }

}%


CCProgram vs %{
precision highp float;

#include <cc-global>
#include <cc-local>

in vec3 a_position;
in vec4 a_color;
out vec4 v_color;

in vec2 a_uv0;
out vec2 v_uv0;

void main() {
  vec4 pos = vec4(a_position, 1);
  
  pos = cc_matViewProj * pos;
  v_uv0 = a_uv0;
  
  v_color = a_color;
  
  gl_Position = pos;
}
}%


CCProgram fs %{
precision highp float;

#include <alpha-test>
#include <texture>

in vec4 v_color;

in vec2 v_uv0;
uniform sampler2D texture;

uniform Properties {
  vec4 in_color;
};

void main() {
  vec4 color = v_color * texture(texture, v_uv0);
  
  if (color.r > 0.2 || color.g > 0.2 || color.b > 0.2) {
    color.rgb = v_color.rgb;
  } else {
    color.rgb = vec3(0.0, 0.0, 0.0);
  };
  
  if ((color.r <= 0.2 && color.g <= 0.2 && color.b <= 0.2)) {
    color.rgba = vec4(1.0, 1.0, 1.0, color.a / v_color.a);
  } else {
    
    color.a *= 0.9;
  };
  
  gl_FragColor = color;
}
}%
