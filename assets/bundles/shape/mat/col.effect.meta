{"ver": "1.0.27", "uuid": "3904e23c-8094-4b0d-81e6-f509a2ff5907", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\nvoid main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * pos;\n  v_uv0 = a_uv0;\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n#endif\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nuniform sampler2D texture;\nvoid main() {\n  vec4 color = v_color * texture2D(texture, v_uv0);\n  if (color.r > 0.2 || color.g > 0.2 || color.b > 0.2) {\n    color.rgb = v_color.rgb;\n  } else {\n    color.rgb = vec3(0.0, 0.0, 0.0);\n  };\n  if ((color.r <= 0.2 && color.g <= 0.2 && color.b <= 0.2)) {\n    color.rgba = vec4(1.0, 1.0, 1.0, color.a / v_color.a);\n  } else {\n    color.a *= 0.9;\n  };\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\nin vec2 a_uv0;\nout vec2 v_uv0;\nvoid main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * pos;\n  v_uv0 = a_uv0;\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nin vec4 v_color;\nin vec2 v_uv0;\nuniform sampler2D texture;\nuniform Properties {\n  vec4 in_color;\n};\nvoid main() {\n  vec4 color = v_color * texture(texture, v_uv0);\n  if (color.r > 0.2 || color.g > 0.2 || color.b > 0.2) {\n    color.rgb = v_color.rgb;\n  } else {\n    color.rgb = vec3(0.0, 0.0, 0.0);\n  };\n  if ((color.r <= 0.2 && color.g <= 0.2 && color.b <= 0.2)) {\n    color.rgba = vec4(1.0, 1.0, 1.0, color.a / v_color.a);\n  } else {\n    color.a *= 0.9;\n  };\n  gl_FragColor = color;\n}"}}], "subMetas": {}}