[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "block63", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [28.339, 360.897, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ce2b70f7-5455-48d8-b832-a234ec6a6f90"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -4.851485148514854, "y": 90}, {"__type__": "cc.Vec2", "x": -25.643564356435647, "y": 80.40983606557378}, {"__type__": "cc.Vec2", "x": -38.11881188118812, "y": 61.96721311475412}, {"__type__": "cc.Vec2", "x": -40.1980198019802, "y": 38.36065573770492}, {"__type__": "cc.Vec2", "x": -30.4950495049505, "y": 16.967213114754102}, {"__type__": "cc.Vec2", "x": -54.75247524752476, "y": 22.13114754098362}, {"__type__": "cc.Vec2", "x": -69.3069306930693, "y": 7.377049180327873}, {"__type__": "cc.Vec2", "x": -65.84158415841584, "y": -13.278688524590166}, {"__type__": "cc.Vec2", "x": -39.504950495049506, "y": -29.50819672131147}, {"__type__": "cc.Vec2", "x": -54.75247524752476, "y": -61.22950819672131}, {"__type__": "cc.Vec2", "x": -51.98019801980198, "y": -81.14754098360656}, {"__type__": "cc.Vec2", "x": -26.336633663366342, "y": -88.52459016393442}, {"__type__": "cc.Vec2", "x": -9.702970297029708, "y": -61.22950819672131}, {"__type__": "cc.Vec2", "x": 3.465346534653463, "y": -56.803278688524586}, {"__type__": "cc.Vec2", "x": 22.871287128712865, "y": -86.31147540983606}, {"__type__": "cc.Vec2", "x": 47.821782178217816, "y": -86.31147540983606}, {"__type__": "cc.Vec2", "x": 56.13861386138613, "y": -65.65573770491804}, {"__type__": "cc.Vec2", "x": 38.11881188118811, "y": -30.24590163934426}, {"__type__": "cc.Vec2", "x": 70, "y": -5.163934426229503}, {"__type__": "cc.Vec2", "x": 61.68316831683168, "y": 19.18032786885246}, {"__type__": "cc.Vec2", "x": 31.188118811881182, "y": 16.967213114754102}, {"__type__": "cc.Vec2", "x": 40.891089108910876, "y": 39.8360655737705}, {"__type__": "cc.Vec2", "x": 31.188118811881182, "y": 75.24590163934428}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 63, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]