[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "block35", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 410, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d561b2d7-b936-429c-890f-c657c2fbd425"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 35, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -19, "y": 205}, {"__type__": "cc.Vec2", "x": -75, "y": 192}, {"__type__": "cc.Vec2", "x": -125, "y": 164}, {"__type__": "cc.Vec2", "x": -167, "y": 121}, {"__type__": "cc.Vec2", "x": -194, "y": 70}, {"__type__": "cc.Vec2", "x": -201, "y": -45}, {"__type__": "cc.Vec2", "x": -179, "y": -102}, {"__type__": "cc.Vec2", "x": -140, "y": -151}, {"__type__": "cc.Vec2", "x": -98, "y": -181}, {"__type__": "cc.Vec2", "x": -38, "y": -202}, {"__type__": "cc.Vec2", "x": 23, "y": -204}, {"__type__": "cc.Vec2", "x": 78, "y": -190}, {"__type__": "cc.Vec2", "x": 166, "y": -121}, {"__type__": "cc.Vec2", "x": 192, "y": -73}, {"__type__": "cc.Vec2", "x": 203, "y": -1}, {"__type__": "cc.Vec2", "x": 124, "y": 1}, {"__type__": "cc.Vec2", "x": 92, "y": -89}, {"__type__": "cc.Vec2", "x": -7, "y": -129}, {"__type__": "cc.Vec2", "x": -77, "y": -106}, {"__type__": "cc.Vec2", "x": -132, "y": -1}, {"__type__": "cc.Vec2", "x": -73, "y": 112}, {"__type__": "cc.Vec2", "x": 68, "y": 112}, {"__type__": "cc.Vec2", "x": 122, "y": 16}, {"__type__": "cc.Vec2", "x": 204, "y": 15}, {"__type__": "cc.Vec2", "x": 145, "y": 146}, {"__type__": "cc.Vec2", "x": 69, "y": 194}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]