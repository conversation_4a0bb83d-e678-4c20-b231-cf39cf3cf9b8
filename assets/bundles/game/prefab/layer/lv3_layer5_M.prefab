[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "lv3_layer5_M", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 32}, {"__id__": 62}, {"__id__": 86}], "_active": true, "_components": [{"__id__": 110}], "_prefab": {"__id__": 111}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "block5", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 15}, {"__id__": 21}], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [21.28, 303.457, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57.287, 54.946, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "09JdWr3UNDYYmt8Jm0CEcp", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-53.641, -59.289, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 9}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "3ck9sv/xVDCb7jdDxLe59s", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [49.657, 53.731, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "a70Ti9QCFJwbUZ9QsCKVku", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [53.303, -60.504, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 21}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "454VgpKPFNPZCvDOQEVJyh", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2de99a80-945c-4a13-9792-a0e3bad649cc"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -86.11111111111111, "y": 100}, {"__type__": "cc.Vec2", "x": -96.52777777777777, "y": 93.75}, {"__type__": "cc.Vec2", "x": -100, "y": 85.41666666666666}, {"__type__": "cc.Vec2", "x": -100, "y": -86.11111111111111}, {"__type__": "cc.Vec2", "x": -96.52777777777777, "y": -93.75}, {"__type__": "cc.Vec2", "x": -86.11111111111111, "y": -100}, {"__type__": "cc.Vec2", "x": 86.11111111111111, "y": -100}, {"__type__": "cc.Vec2", "x": 93.75, "y": -96.52777777777777}, {"__type__": "cc.Vec2", "x": 100, "y": -86.11111111111111}, {"__type__": "cc.Vec2", "x": 100, "y": 86.11111111111111}, {"__type__": "cc.Vec2", "x": 96.52777777777777, "y": 93.75}, {"__type__": "cc.Vec2", "x": 85.41666666666666, "y": 100}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 5, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "fb0eefaf-b4eb-48d5-a757-8b496f9c9155"}, "fileId": "37btfDCd1Lo74I4T1ADROq", "sync": false}, {"__type__": "cc.Node", "_name": "block5 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 33}, {"__id__": 39}, {"__id__": 45}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [245.772, 303.457, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57.287, 54.946, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "09JdWr3UNDYYmt8Jm0CEcp", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-53.641, -59.289, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "3ck9sv/xVDCb7jdDxLe59s", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [49.657, 53.731, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 45}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "a70Ti9QCFJwbUZ9QsCKVku", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [53.303, -60.504, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "454VgpKPFNPZCvDOQEVJyh", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2de99a80-945c-4a13-9792-a0e3bad649cc"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -86.11111111111111, "y": 100}, {"__type__": "cc.Vec2", "x": -96.52777777777777, "y": 93.75}, {"__type__": "cc.Vec2", "x": -100, "y": 85.41666666666666}, {"__type__": "cc.Vec2", "x": -100, "y": -86.11111111111111}, {"__type__": "cc.Vec2", "x": -96.52777777777777, "y": -93.75}, {"__type__": "cc.Vec2", "x": -86.11111111111111, "y": -100}, {"__type__": "cc.Vec2", "x": 86.11111111111111, "y": -100}, {"__type__": "cc.Vec2", "x": 93.75, "y": -96.52777777777777}, {"__type__": "cc.Vec2", "x": 100, "y": -86.11111111111111}, {"__type__": "cc.Vec2", "x": 100, "y": 86.11111111111111}, {"__type__": "cc.Vec2", "x": 96.52777777777777, "y": 93.75}, {"__type__": "cc.Vec2", "x": 85.41666666666666, "y": 100}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 5, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 32}, "asset": {"__uuid__": "fb0eefaf-b4eb-48d5-a757-8b496f9c9155"}, "fileId": "6frTSbZghAkYydNYmve3jI", "sync": false}, {"__type__": "cc.Node", "_name": "block3", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 63}, {"__id__": 69}, {"__id__": 75}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}, {"__id__": 83}, {"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.789, -100.872, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50.37, 24.274, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 63}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "63+V1lU9xIyq3NGQd33byN", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55.468, 28.345, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 69}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "b05QlJp3tCX54MoVAvprNI", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.514, -40.857, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 75}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "ca6/GV34JLZZuwZjxBOSI3", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -56, "y": 78}, {"__type__": "cc.Vec2", "x": -88, "y": 58}, {"__type__": "cc.Vec2", "x": -95, "y": 18}, {"__type__": "cc.Vec2", "x": -0.6, "y": -77.3}, {"__type__": "cc.Vec2", "x": 96, "y": 22}, {"__type__": "cc.Vec2", "x": 78.5, "y": 68}, {"__type__": "cc.Vec2", "x": 45.7, "y": 78}, {"__type__": "cc.Vec2", "x": -1, "y": 51}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 62}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "4d0kFDNxtONZA2MNmcJ/Gg", "sync": false}, {"__type__": "cc.Node", "_name": "block3 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 87}, {"__id__": 93}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 106}, {"__id__": 107}, {"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [241.572, -100.872, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50.37, 24.274, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 87}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "63+V1lU9xIyq3NGQd33byN", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55.468, 28.345, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 93}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "b05QlJp3tCX54MoVAvprNI", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 100}, {"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.514, -40.857, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 99}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "ca6/GV34JLZZuwZjxBOSI3", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -56, "y": 78}, {"__type__": "cc.Vec2", "x": -88, "y": 58}, {"__type__": "cc.Vec2", "x": -95, "y": 18}, {"__type__": "cc.Vec2", "x": -0.6, "y": -77.3}, {"__type__": "cc.Vec2", "x": 96, "y": 22}, {"__type__": "cc.Vec2", "x": 78.5, "y": 68}, {"__type__": "cc.Vec2", "x": 45.7, "y": 78}, {"__type__": "cc.Vec2", "x": -1, "y": 51}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 86}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "08KvItlKhNnJN2AHIjiZwk", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "layerIndex": 5, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]