[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "lv4_layer16_L", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 26}, {"__id__": 50}, {"__id__": 68}, {"__id__": 86}, {"__id__": 110}], "_active": true, "_components": [{"__id__": 134}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "block28", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-82.107, 304.405, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [69.478, 1.755, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "fcv9LMD1dENLtTXw71XtcM", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.728, -62.331, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 9}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "16BlTSbYpFhYkdx+nSUQCR", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-71.154, -0.026, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "9aLs/DWJBBoI3r62HL9wPq", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "7dqQbGhhVKGrGxnJk8A5ci", "sync": false}, {"__type__": "cc.Node", "_name": "block28 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 27}, {"__id__": 33}, {"__id__": 39}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.228, -83.668, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [69.478, 1.755, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "fcv9LMD1dENLtTXw71XtcM", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.728, -62.331, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "16BlTSbYpFhYkdx+nSUQCR", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-71.154, -0.026, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "9aLs/DWJBBoI3r62HL9wPq", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 26}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "c5zk0dUSRGFq2d3rdWQn50", "sync": false}, {"__type__": "cc.Node", "_name": "block28 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 51}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [136.852, 304.405, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.612, 42.698, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "fcv9LMD1dENLtTXw71XtcM", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.612, -44.529, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 57}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "9bchEWW79L7p7NMhkMeXk7", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 50}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "4d8elVq+9MsbQxU5KVzvqe", "sync": false}, {"__type__": "cc.Node", "_name": "block28 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 69}, {"__id__": 75}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}, {"__id__": 83}, {"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131.731, -83.668, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [58.797, -0.026, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 69}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "fcv9LMD1dENLtTXw71XtcM", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-62.254, -1.805, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 75}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "9bchEWW79L7p7NMhkMeXk7", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 68}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "0bKxPHmkRLFrmul4MpNkvM", "sync": false}, {"__type__": "cc.Node", "_name": "block28 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 87}, {"__id__": 93}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 106}, {"__id__": 107}, {"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [356.811, 304.405, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [67.698, 0.975, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 87}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "e7q/sSD09B7pDXcbtOYcz2", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.508, 59.719, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 93}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "63p+uN0iZHq5yfAU7LZmf1", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 100}, {"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-72.934, -0.806, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 99}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "dcQn+XoixGjqI000463+eL", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 86}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "88YcTpRypIOqcdkZQpxj3H", "sync": false}, {"__type__": "cc.Node", "_name": "block28 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 111}, {"__id__": 117}, {"__id__": 123}], "_active": true, "_components": [{"__id__": 129}, {"__id__": 130}, {"__id__": 131}, {"__id__": 132}], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 204}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [351.69, -83.668, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 113}, {"__id__": 114}, {"__id__": 115}], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [67.698, 0.975, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 111}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "e7q/sSD09B7pDXcbtOYcz2", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}, {"__id__": 120}, {"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.508, 59.719, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 117}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "63p+uN0iZHq5yfAU7LZmf1", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 127}], "_prefab": {"__id__": 128}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-72.934, -0.806, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 123}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "dcQn+XoixGjqI000463+eL", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cefe9bea-6a62-4cc3-9f30-0c50e6df3799"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 28, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.26, "y": 85.05}, {"__type__": "cc.Vec2", "x": -55.08, "y": 75.33}, {"__type__": "cc.Vec2", "x": -93.96, "y": 5.670000000000002}, {"__type__": "cc.Vec2", "x": -89.1, "y": -17.820000000000007}, {"__type__": "cc.Vec2", "x": -49.41, "y": -81}, {"__type__": "cc.Vec2", "x": -40.5, "y": -85.05}, {"__type__": "cc.Vec2", "x": 47.790000000000006, "y": -81.81}, {"__type__": "cc.Vec2", "x": 57.510000000000005, "y": -72.09}, {"__type__": "cc.Vec2", "x": 93.96, "y": -4.859999999999999}, {"__type__": "cc.Vec2", "x": 89.90999999999998, "y": 15.39}, {"__type__": "cc.Vec2", "x": 49.40999999999998, "y": 80.18999999999998}, {"__type__": "cc.Vec2", "x": 37.260000000000005, "y": 85.05}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "4100da16-a866-4626-91ba-999d80e04970"}, "fileId": "45L26NUdJELb/tNyUGLwPv", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "layerIndex": 16, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]