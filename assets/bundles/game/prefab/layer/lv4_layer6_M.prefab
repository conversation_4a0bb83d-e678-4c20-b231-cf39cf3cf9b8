[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "lv4_layer6_M", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 26}, {"__id__": 50}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 98}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "block3", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 234}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [138.717, 277.411, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-76.673, 51.109, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "35VmD7q9xEYZmDPrE3qA9l", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [77.859, 51.109, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 9}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "4c2h48VzVFPrjMKtFZdfJl", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.593, -48.882, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "24zwA2w9pBWKT2PCf/comx", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -84, "y": 117}, {"__type__": "cc.Vec2", "x": -132, "y": 87}, {"__type__": "cc.Vec2", "x": -142.5, "y": 27}, {"__type__": "cc.Vec2", "x": -12, "y": -117}, {"__type__": "cc.Vec2", "x": 33, "y": -94.5}, {"__type__": "cc.Vec2", "x": 144, "y": 33}, {"__type__": "cc.Vec2", "x": 136.5, "y": 79.5}, {"__type__": "cc.Vec2", "x": 100.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": 43.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": -1.5, "y": 76.5}, {"__type__": "cc.Vec2", "x": -43.5, "y": 112.5}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "03qHvonsBMgKmG9VMFA9GZ", "sync": false}, {"__type__": "cc.Node", "_name": "block3 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 27}, {"__id__": 33}, {"__id__": 39}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 234}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.365, 138.787, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80.652, 53.948, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "a10WnipjpKPIQLiu1/NDuc", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-92.06, 53.948, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "ecFFDfZQBAka/RmkM2jNCb", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.159, -39.226, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "26W3ssIDdIMI0ecY9Ih2E5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -84, "y": 117}, {"__type__": "cc.Vec2", "x": -132, "y": 87}, {"__type__": "cc.Vec2", "x": -142.5, "y": 27}, {"__type__": "cc.Vec2", "x": -12, "y": -117}, {"__type__": "cc.Vec2", "x": 33, "y": -94.5}, {"__type__": "cc.Vec2", "x": 144, "y": 33}, {"__type__": "cc.Vec2", "x": 136.5, "y": 79.5}, {"__type__": "cc.Vec2", "x": 100.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": 43.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": -1.5, "y": 76.5}, {"__type__": "cc.Vec2", "x": -43.5, "y": 112.5}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 26}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "70xIo9pllOH5KCntlI7aDR", "sync": false}, {"__type__": "cc.Node", "_name": "block3 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 51}, {"__id__": 57}, {"__id__": 63}], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 234}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [138.717, 0.162, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [78.946, 53.427, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -180}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "08yFwdu2lMK64U4lKi9BuU", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-75.586, 53.427, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -180}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 57}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "85+ta8n6NHH7EzA2NiCCyL", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.68, -39.747, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -180}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 63}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "dcLLd06ohDjqxckQuesyXk", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -84, "y": 117}, {"__type__": "cc.Vec2", "x": -132, "y": 87}, {"__type__": "cc.Vec2", "x": -142.5, "y": 27}, {"__type__": "cc.Vec2", "x": -12, "y": -117}, {"__type__": "cc.Vec2", "x": 33, "y": -94.5}, {"__type__": "cc.Vec2", "x": 144, "y": 33}, {"__type__": "cc.Vec2", "x": 136.5, "y": 79.5}, {"__type__": "cc.Vec2", "x": 100.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": 43.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": -1.5, "y": 76.5}, {"__type__": "cc.Vec2", "x": -43.5, "y": 112.5}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 50}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "cfRyGVR85Ippt5gdrYhqWN", "sync": false}, {"__type__": "cc.Node", "_name": "block3 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 75}, {"__id__": 81}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}, {"__id__": 95}, {"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 234}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [275.796, 138.786, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 270}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-80.653, 52.133, 0, 0, 0, -0.7071067811865476, -0.7071067811865475, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -270}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 75}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "07kO2j37lGJ4DW8yK/HOk8", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 82}, {"__id__": 83}, {"__id__": 84}, {"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [92.059, 52.133, 0, 0, 0, -0.7071067811865476, -0.7071067811865475, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -270}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 81}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "73I9KDTtpBT7mXu44wWcRw", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.158, -45.585, 0, 0, 0, -0.7071067811865476, -0.7071067811865475, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -270}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 87}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "35VoAxsGVBsJrVortFuYkC", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "35c4c5f0-2d67-42b2-90cb-54a006578817"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -84, "y": 117}, {"__type__": "cc.Vec2", "x": -132, "y": 87}, {"__type__": "cc.Vec2", "x": -142.5, "y": 27}, {"__type__": "cc.Vec2", "x": -12, "y": -117}, {"__type__": "cc.Vec2", "x": 33, "y": -94.5}, {"__type__": "cc.Vec2", "x": 144, "y": 33}, {"__type__": "cc.Vec2", "x": 136.5, "y": 79.5}, {"__type__": "cc.Vec2", "x": 100.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": 43.5, "y": 112.5}, {"__type__": "cc.Vec2", "x": -1.5, "y": 76.5}, {"__type__": "cc.Vec2", "x": -43.5, "y": 112.5}], "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 3, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 74}, "asset": {"__uuid__": "33f584df-7ee6-4053-ad0d-0483378b6545"}, "fileId": "40Nu61XKZC7pfuuEKc8WgV", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "layerIndex": 6, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]