[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "lv4_layer2", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "block15", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 192}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [205.975, 170.429, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.339, 38.281, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "4bJRO7h0hIcJaQuWYB/lrx", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.339, -59.438, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": ""}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "pinColor": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 9}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "751cRvduNAFbmS0FQa8EI1", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7dc82692-ebb4-4289-8d83-cbdebd30de02"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 15, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -10, "y": 96}, {"__type__": "cc.Vec2", "x": -60, "y": 76}, {"__type__": "cc.Vec2", "x": -91, "y": 33}, {"__type__": "cc.Vec2", "x": -95, "y": -18}, {"__type__": "cc.Vec2", "x": -70, "y": -67}, {"__type__": "cc.Vec2", "x": -18, "y": -95}, {"__type__": "cc.Vec2", "x": 33, "y": -91}, {"__type__": "cc.Vec2", "x": 76, "y": -60}, {"__type__": "cc.Vec2", "x": 96, "y": -10}, {"__type__": "cc.Vec2", "x": 91, "y": 33}, {"__type__": "cc.Vec2", "x": 70, "y": 67}, {"__type__": "cc.Vec2", "x": 33, "y": 91}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "debe1747-7071-43ad-b995-8d81f777d102"}, "fileId": "8cH2gYPxpJFYPkfaE27ilg", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "layerIndex": 2, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]