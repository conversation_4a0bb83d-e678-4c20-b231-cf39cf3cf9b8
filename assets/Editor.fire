[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "ab781ac5-a3dd-494d-8844-6d44b05a616e"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 74}, {"__id__": 81}, {"__id__": 88}, {"__id__": 95}, {"__id__": 97}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 290}, {"__id__": 291}, {"__id__": 292}, {"__id__": 293}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [375, 812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.Node", "_name": "Game", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__id__": 9}, {"__id__": 33}, {"__id__": 60}], "_active": true, "_components": [{"__id__": 73}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9aynfVEuZGWqYaDOKdHIG5"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 220, "g": 225, "b": 235, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c8umrvh7xL25liSSHJT+Cx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ceHxd5SwpL4rhYAXg8/Kmp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "97mrst7rNOSo5fqwEHOqfG"}, {"__type__": "cc.Node", "_name": "frame", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 10}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 516, "height": 415}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 497, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6bv1t30cJPoIurWLUGbPWl"}, {"__type__": "cc.Node", "_name": "level", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 202, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7emrOWDFJAFbn2IGVXnkmI"}, {"__type__": "cc.Node", "_name": "levelLab", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 52, "g": 68, "b": 89, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.411, -4.523, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "21V5neK4lOW5UdhIEIqxFL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "LEVEL 2", "_N$string": "LEVEL 2", "_fontSize": 26, "_lineHeight": 26, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "a38xCSWBlGIrfK4IvqxlaM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a254e9f-ef90-4b94-9a5e-5c189bb89c6a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "63RfFfu9RDVJi/5rN3+mSU"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 15}], "_active": true, "_components": [{"__id__": 30}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 374}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "21NIfZzO5PgKlcGXWAVCgi"}, {"__type__": "cc.Node", "_name": "TextureFill", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 16}, {"__id__": 19}, {"__id__": 22}, {"__id__": 25}], "_active": false, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "efJS12Ak9LWIiJgN/Ss2UW"}, {"__type__": "cc.Node", "_name": "origin", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3000, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7bMvRSjt5HGIdAg+tywi8R"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f8388720-360c-4e12-ac90-5aa53b551c26"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "69kF+QpgpBAIVSOs/qyQB2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "e5f3676e-ff85-4621-8cd6-93b0d7d4febd"}, "fileId": "75bjbqX7BNTIOw99Nu/qCN", "sync": false}, {"__type__": "cc.Node", "_name": "shape", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3000, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "085JJRdmdPfahzVqOdPYlR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d74cc0c4-27b0-4341-a268-03a4f3c515b1"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "9bzC5o0HVG64PJHT1qEnh1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "e5f3676e-ff85-4621-8cd6-93b0d7d4febd"}, "fileId": "fd8FGpH3RLR4lP0Xs7nZEa", "sync": false}, {"__type__": "cc.Node", "_name": "start", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3000, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0eJe9m/MFGZJPl8XGrsnsh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2b402ddc-bbdd-4929-8af6-80c66c680d93"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e4VSVtaHVFQrmJj8LITzLy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "e5f3676e-ff85-4621-8cd6-93b0d7d4febd"}, "fileId": "b7HETJfFBHEpWB7dAfimoh", "sync": false}, {"__type__": "cc.Node", "_name": "display", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 26}], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 483, "height": 483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "21pKFbAgRFhqCrVhlu9wNR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "f4wnMWqjtAFqfpS5mYDxFD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "e5f3676e-ff85-4621-8cd6-93b0d7d4febd"}, "fileId": "ab+4mljmxIcYS2dr/gyLnl", "sync": false}, {"__type__": "bf386Dq77ZNpoWmryS7lWC5", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "origin": {"__id__": 16}, "shape": {"__id__": 19}, "startN": {"__id__": 22}, "display": {"__id__": 26}, "_id": "6a6LoQY61El6a6R4fMPVmr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "e5f3676e-ff85-4621-8cd6-93b0d7d4febd"}, "fileId": "", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": "b2QiF7mrNJH4SBq/KO8LUw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0fed045c-02b0-4868-b023-62a7843cabef"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "74PNoWUyBFhZYZbw4npYPg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 107.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "4ciCTGYMtHH484X7CO1jBS"}, {"__type__": "cc.Node", "_name": "cmpParent", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 34}, {"__id__": 40}, {"__id__": 46}, {"__id__": 52}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 737, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 228, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9dxNHSDXtBtoQDxl1YojVb"}, {"__type__": "cc.Node", "_name": "pen_s_empty", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}, {"__id__": 37}], "_active": true, "_components": [{"__id__": 39}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-265, 10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1emwIDdLhDzLila2WBty9x"}, {"__type__": "cc.Node", "_name": "lock_pen", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 17, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6bYE6js9FIKIEG287UUpbN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "519b7e4c-1ecd-4e77-b773-70b4e0e863f0"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "eePmkIPXVMnLCUGGyolOVv"}, {"__type__": "cc.Node", "_name": "fill", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5bd5W86V1Db4MhjohHFYL/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2475ea72-8e41-43e8-bcbe-71f426fd771e"}, "_type": 3, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "15eHXm67FPe4UPr0Obr5in"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a7da1c9f-b862-4f3e-bdb4-4a3c35ea53ac"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6e3bE1RRRENaF+HdqeybCn"}, {"__type__": "cc.Node", "_name": "pen_s_empty", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 41}, {"__id__": 43}], "_active": true, "_components": [{"__id__": 45}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-90, 10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c7CwVVF6pN/4cDib/fB0n1"}, {"__type__": "cc.Node", "_name": "lock_pen", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 17, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "25zvWOhK9Kh4dFDB35keae"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "519b7e4c-1ecd-4e77-b773-70b4e0e863f0"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7bFSidkCNDr5fw3cAmy5dH"}, {"__type__": "cc.Node", "_name": "fill", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 44}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 13}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1drW8dAS1EHIKXWqzIciO3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2475ea72-8e41-43e8-bcbe-71f426fd771e"}, "_type": 3, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "88vVi8K4BCXrK1EkJYMV2U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a7da1c9f-b862-4f3e-bdb4-4a3c35ea53ac"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "886hDercxOMrlANKhKm/Wu"}, {"__type__": "cc.Node", "_name": "pen_s_empty", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 47}, {"__id__": 49}], "_active": true, "_components": [{"__id__": 51}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [85, 10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "73nem37l9D3ZuIjusPNfz0"}, {"__type__": "cc.Node", "_name": "lock_pen", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 48}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 17, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a8AzWqk6lGW7MB/k6XNUqX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "519b7e4c-1ecd-4e77-b773-70b4e0e863f0"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "69GXsXuMRHVJpyS26de0Xw"}, {"__type__": "cc.Node", "_name": "fill", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 13}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "11QbTCxupPHKlG3vir7dtM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2475ea72-8e41-43e8-bcbe-71f426fd771e"}, "_type": 3, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "25J0aVFx5Ld6GTW3ObB+Vv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a7da1c9f-b862-4f3e-bdb4-4a3c35ea53ac"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0bPbrrtDJJHYGujuiC2URR"}, {"__type__": "cc.Node", "_name": "pen_s_empty", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 53}, {"__id__": 55}], "_active": true, "_components": [{"__id__": 57}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [260, 10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "06snUykbxIY4M3B/ye9dnA"}, {"__type__": "cc.Node", "_name": "lock_pen", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [{"__id__": 54}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 17, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1cwo/JHeFKRJHMKpg5qT5W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "519b7e4c-1ecd-4e77-b773-70b4e0e863f0"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "17H/v3LDVDKbyiWR1Ual9o"}, {"__type__": "cc.Node", "_name": "fill", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [{"__id__": 56}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 13}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8afL9ihzxMOLoOvvqg1++W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2475ea72-8e41-43e8-bcbe-71f426fd771e"}, "_type": 3, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "52614b9QlBsKCxCn0KX+fq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a7da1c9f-b862-4f3e-bdb4-4a3c35ea53ac"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c751xH39xGAYUQ5W074YG4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "eb321dce-547b-4cec-b61a-df342191868d"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "03p7Iedy9K4LnuinF3crwq"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 26, "_N$paddingRight": 36, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "36n1mLY9xMq4snJumz/JGL"}, {"__type__": "cc.Node", "_name": "t<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 61}, {"__id__": 63}, {"__id__": 65}, {"__id__": 67}, {"__id__": 69}], "_active": true, "_components": [{"__id__": 71}, {"__id__": 72}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 356, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 121, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "068Q2pOZJP2b3y9pUJK9zt"}, {"__type__": "cc.Node", "_name": "paint_01", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "570QAGSXhN8KG8CzbRwAxP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b8niwLq9FKgZSMgEe/WEHE"}, {"__type__": "cc.Node", "_name": "paint_01", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 64}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bjRGyCpZM+KV9cloyQft9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "29qdOXqPZNlrCkX4EMJMAr"}, {"__type__": "cc.Node", "_name": "paint_01", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 66}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "83EF8TxzVITbWwBI/WFqHA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "33q9yawStDK6ockKum1CjM"}, {"__type__": "cc.Node", "_name": "paint_01", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 68}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [68, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1aiH8E3GNCqZ9CqLpErJwn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "58ZuegSJFG6owruvzw1XzH"}, {"__type__": "cc.Node", "_name": "paint_01", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 70}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [134, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6cBl4+Z4tPjJR0X6ncqxfJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "aekfMJfahB7I1szijZsVub"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d6e46e61-3767-474b-98f2-95db27c21fd9"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "63jNn2DWlIAott/0afpSas"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 356, "height": 61}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 24, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 18, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "bb07xmDcBL5LF/iM6wMZPs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2aX4tOGGVGY59Hyrggi2ls"}, {"__type__": "cc.Node", "_name": "ExportBtn", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 75}], "_active": true, "_components": [{"__id__": 80}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-301.145, 630.345, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "actBR5TAFOjYOvGOtzS1nz"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 74}, "_children": [{"__id__": 76}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1fBX54QDhDnIwT41eI0NHU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "89EpGLHbpNzolA+Z/5n/vC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "export", "_N$string": "export", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "e5PoVg5HFAYJvtsFvQLk8o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "51uhY0LmxEBKRI8WxuL/Z5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "26Z7YQmH9G8omHjgMt8Nyv"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 75}, "_id": "e9Djizl0hGL7OycX0Nq+ms"}, {"__type__": "cc.Node", "_name": "ImportBtn", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 87}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-144.473, 632.552, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "38F1U3sblP+qkuHWfwbEHY"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 81}, "_children": [{"__id__": 83}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 86}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "05bgCzegBLgIDG6hFUMNOZ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b0ckBhORlL+qOn/EJZywZa"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Import", "_N$string": "Import", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "3dROBSfwxDqqSky5zo2A7V"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "8e/Jur71ZM759JQdojGi6D"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "11H4iMT/tEqq2LakF17T5F"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 82}, "_id": "45x55NUdNMdYQ3TkRzak8Z"}, {"__type__": "cc.Node", "_name": "UpdateBtn", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 89}], "_active": true, "_components": [{"__id__": 94}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.855, 632.552, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "eeiAGWn6ROw4lyjkQLFeX/"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 88}, "_children": [{"__id__": 90}], "_active": true, "_components": [{"__id__": 92}, {"__id__": 93}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "25gUeEqNlCSovHL+bNUHj6"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 89}, "_children": [], "_active": true, "_components": [{"__id__": 91}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1eCEBVroxFR4ZLQCU36o6V"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Update", "_N$string": "Update", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "f76AP2wklOcISsg39MjpkP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f2gSwow1NNn5Gahp3tB1oi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "88xaDs9XNF5aPMjgEoSXVs"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 89}, "_id": "b8z4RUxpFEKaj9e57Bv8O0"}, {"__type__": "cc.Node", "_name": "PinCountLabel", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 96}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 649.28, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 752.843, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "90YE11k3xD9Ilm0Huu96/J"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": true, "_N$string": "<color=#ff0000>Total Pin Count: 4 fit pin count is 177</color>", "_N$horizontalAlign": 0, "_N$fontSize": 40, "_N$font": null, "_N$cacheMode": 0, "_N$maxWidth": 0, "_N$lineHeight": 50, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": "24Qo9ckZhInJPY52CLpN00"}, {"__type__": "cc.Node", "_name": "bg_loading", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 249, "g": 151, "b": 151, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 730, "height": 780.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -341.64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "72JhVz6aFD/p5xosapHAId"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "18fc3fdb-ef2e-4e1b-9eb5-5868646e2e98"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bcW/4jLgJCdpmA0C1Twnkq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 10, "_right": 10, "_top": 0.47, "_bottom": 80, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1624, "_id": "57xJ9O9kVBaaEEw5naNkv+"}, {"__type__": "cc.Node", "_name": "LayerContainer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 101}, {"__id__": 140}, {"__id__": 179}, {"__id__": 224}, {"__id__": 245}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a6B3W5PANHhJB7M/rwTdNQ"}, {"__type__": "cc.Node", "_name": "lv6_layer5", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 102}, {"__id__": 120}], "_active": true, "_components": [{"__id__": 138}], "_prefab": {"__id__": 139}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6cNOgwMflFg5+LyH+ZsIje"}, {"__type__": "cc.Node", "_name": "block14", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.972, -17.875, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "19gyN3NoFKMKnl6AJTG0yQ"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 104}, {"__id__": 105}, {"__id__": 106}, {"__id__": 107}], "_prefab": {"__id__": 108}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.518, 47.046, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "b8m4KlaTBASI/pCz0yV+xp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dfXYFhPepJsLRp3oKOSMxE"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "76MJMplf9G6JhGqxgl43AF"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "4dXmzzKTlDTZw6Nnk2gqWU"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "pinColor": 1, "_id": "b5QoVJuvlA9qt5FzfDGv6o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 103}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "b9qwn161lBXrynKXzFT0jm", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}, {"__id__": 112}, {"__id__": 113}], "_prefab": {"__id__": 114}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.518, -40.023, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "72dyqEPJ9Mbo8MeYimQUhX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "41WHlfOu9KAKEh2+mdmhI5"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "ebNfdZKdpDOYr5c0nypMVp"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "148quNxRpBLZS1cp9p0EGC"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "pinColor": 1, "_id": "98okPCxuRJkrO4yS5DGyBB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "6euXeZfUtL0bNvwKEKo7RU", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9f3832fb-6b54-4c0b-8fc8-bebb00315732"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "beEGs5hXVFM5I1cUDlZdJf"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "5cwjMkA/hGH7UiyLPe2KAT"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -4.5, "y": 80}, {"__type__": "cc.Vec2", "x": -25.5, "y": 72.77777777777777}, {"__type__": "cc.Vec2", "x": -36, "y": 58.33333333333334}, {"__type__": "cc.Vec2", "x": -36, "y": -58.33333333333333}, {"__type__": "cc.Vec2", "x": -25.5, "y": -72.77777777777777}, {"__type__": "cc.Vec2", "x": -4.5, "y": -80}, {"__type__": "cc.Vec2", "x": 21.75, "y": -75}, {"__type__": "cc.Vec2", "x": 36, "y": -58.33333333333333}, {"__type__": "cc.Vec2", "x": 36, "y": 58.33333333333334}, {"__type__": "cc.Vec2", "x": 21.75, "y": 75}], "_id": "b7pp3eCA1CfL2RCGe6+OCx"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 14, "_id": "51z7HOvhVIYJEJfnJbron5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 102}, "asset": {"__uuid__": "e5567161-bfe8-4323-ba75-828dc805ba0e"}, "fileId": "33m04X4lFHmpQD89DLLiJL", "sync": false}, {"__type__": "cc.Node", "_name": "block14 copy", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 121}, {"__id__": 127}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}, {"__id__": 135}, {"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [270.101, -17.875, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e8QUbVPwND87+959UVNiAd"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 123}, {"__id__": 124}, {"__id__": 125}], "_prefab": {"__id__": 126}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.512, 43.86, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "61T7XQtbRL177vISpt+klV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bbQgWZ3W9Ec6Uu40EcXEc1"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "e7FDUOJvZDDZ2IcCFzr4Mj"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "21IWBbSX9Acqimg4obDJSK"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "pinColor": 1, "_id": "8eMmQJ0g9LoJM8GEC/iwH6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 121}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "f2/woJCU5LrbEOFDx0U3oF", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 128}, {"__id__": 129}, {"__id__": 130}, {"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.108, -41.805, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "b43c/Q/HFGxphV4yCF2DkR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e91Ej+YL1NWaIxxP5BNd7p"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "4fiofeVsBEALgVzGAd58wo"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "e5hxn04vxMx58I2r1D1oCP"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "pinColor": 1, "_id": "baaA77BxNNJoc5ccfTLO4T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 127}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "e7dG1ZWItOjLykALFzH35t", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9f3832fb-6b54-4c0b-8fc8-bebb00315732"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d7ZDNhwEpFTpCiPmqvuQdZ"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "b8ieZ/uGFC95VnMW6UKqyN"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -4.5, "y": 80}, {"__type__": "cc.Vec2", "x": -25.5, "y": 72.77777777777777}, {"__type__": "cc.Vec2", "x": -36, "y": 58.33333333333334}, {"__type__": "cc.Vec2", "x": -36, "y": -58.33333333333333}, {"__type__": "cc.Vec2", "x": -25.5, "y": -72.77777777777777}, {"__type__": "cc.Vec2", "x": -4.5, "y": -80}, {"__type__": "cc.Vec2", "x": 21.75, "y": -75}, {"__type__": "cc.Vec2", "x": 36, "y": -58.33333333333333}, {"__type__": "cc.Vec2", "x": 36, "y": 58.33333333333334}, {"__type__": "cc.Vec2", "x": 21.75, "y": 75}], "_id": "c9G4hGa+ZNxJppk+LGk1W2"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 14, "_id": "6fnyKDUzJE4okNoGTFK7db"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 120}, "asset": {"__uuid__": "e5567161-bfe8-4323-ba75-828dc805ba0e"}, "fileId": "3cjUYTfgVEbp/3gOZbgTnn", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "layerIndex": 5, "_id": "63CQ3w0yVGcKqIZwQktixY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 101}, "asset": {"__uuid__": "ed08539b-a69e-4131-9bc1-998e67ebcc72"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "lv6_layer4", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 141}, {"__id__": 159}], "_active": true, "_components": [{"__id__": 177}], "_prefab": {"__id__": 178}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b9c0sqwitK6Z85ppjXM2/X"}, {"__type__": "cc.Node", "_name": "block43", "_objFlags": 0, "_parent": {"__id__": 140}, "_children": [{"__id__": 142}, {"__id__": 148}], "_active": true, "_components": [{"__id__": 154}, {"__id__": 155}, {"__id__": 156}, {"__id__": 157}], "_prefab": {"__id__": 158}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [349.542, 59.581, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ac2V4aBklBuok83QTw6Ssa"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 144}, {"__id__": 145}, {"__id__": 146}], "_prefab": {"__id__": 147}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [37.031, 7.724, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "cddtVdUT1C6q44t48AdX9N"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "27teB4BUZMlpMq+sOB9fFV"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "c9m7lUt3BJBatauJENkNDw"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "36lPM0swNFg7uTDBJ6r4EO"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "pinColor": 1, "_id": "99ej7EJ0tAd7oa86chlRgC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 142}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "abqSySMI5D47Eay/9bRp00", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}, {"__id__": 151}, {"__id__": 152}], "_prefab": {"__id__": 153}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-52.847, 2.107, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "c1ASlR1vZDtZ3xx33KAqHn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "88Or6IrMBKKJq50FuElhwC"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "e1vnx7nBZPS7O2+17/7bP5"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "b7zwMIwwdMo5ev22aeyLFA"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "pinColor": 1, "_id": "0aNUvjp8pLX40rwFAMWH3H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 148}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "d36kgrx4JJypEFd9UPCZtv", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f21718b8-2169-48b7-8ff4-cfba241f1bb9"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "410KK/V9BLfbZ6GGLPfwKM"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 43, "_id": "9ftESp7flMEKEB/zjzNdPq"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "aepJfAUxdKvbQPIbceLRFf"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 10.810810810810807, "y": 50}, {"__type__": "cc.Vec2", "x": -29.549549549549553, "y": 24}, {"__type__": "cc.Vec2", "x": -57.65765765765766, "y": 42.66666666666666}, {"__type__": "cc.Vec2", "x": -80, "y": 30}, {"__type__": "cc.Vec2", "x": -73.51351351351352, "y": -42}, {"__type__": "cc.Vec2", "x": -55.4954954954955, "y": -44.666666666666664}, {"__type__": "cc.Vec2", "x": -28.108108108108112, "y": -25.333333333333336}, {"__type__": "cc.Vec2", "x": -0.7207207207207205, "y": -46.666666666666664}, {"__type__": "cc.Vec2", "x": 38.91891891891892, "y": -48.666666666666664}, {"__type__": "cc.Vec2", "x": 72.07207207207207, "y": -26.666666666666668}, {"__type__": "cc.Vec2", "x": 78.55855855855856, "y": 13.333333333333329}, {"__type__": "cc.Vec2", "x": 51.89189189189187, "y": 44.66666666666666}], "_id": "b8OU1M4UBE7q80xUPw5ans"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 141}, "asset": {"__uuid__": "3ec5473a-3c8c-44d1-a3e7-ed95d95e78ab"}, "fileId": "83sMFv011OPrhoyZ9V5sbQ", "sync": false}, {"__type__": "cc.Node", "_name": "block43 copy", "_objFlags": 0, "_parent": {"__id__": 140}, "_children": [{"__id__": 160}, {"__id__": 166}], "_active": true, "_components": [{"__id__": 172}, {"__id__": 173}, {"__id__": 174}, {"__id__": 175}], "_prefab": {"__id__": 176}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-71.497, 57.595, 0, 0, 0, 0.9238795325112867, 0.38268343236508984, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 135}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ba66vwCZ1DeJbKKhcCgLbp"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}], "_prefab": {"__id__": 165}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [44.237, -3.326, 0, 0, 0, -0.9238795325112867, 0.38268343236508984, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -135}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "a6n6nWFcVAJ4oxc3xiJXXS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "34qhS7LZhMT73Rn5cytCfj"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "84fTDxKkVFaLYgX0GtTwgt"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "babWx+DCNK7Znd5eoovjjq"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "pinColor": 1, "_id": "8b+ZQmHJFFKoSQmeUrKKGr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 160}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "b39HnYDbpPxpNEIBnOf179", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 167}, {"__id__": 168}, {"__id__": 169}, {"__id__": 170}], "_prefab": {"__id__": 171}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-47.045, 0.887, 0, 0, 0, -0.9238795325112867, 0.38268343236508984, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -135}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "5832P4ywtDb5+4wNNxsOKq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "41b+KgDwdHf6yDJ3QQwR4O"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "5fQOMi+n1ARZQvozfphzbg"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "99N50F+xREsqRWZFbSLLAL"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "pinColor": 1, "_id": "dfGwGYDo9JgoZ1sDKTEXHY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "54M6chzxRHaoHqk28ccH2D", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f21718b8-2169-48b7-8ff4-cfba241f1bb9"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "67Op8IpPRPSpj9Fa6SJWdY"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 43, "_id": "79dRiypR5K7ot39l9up7Dk"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "bbB3hPNt9PtIJqBcKnd6yV"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 10.810810810810807, "y": 50}, {"__type__": "cc.Vec2", "x": -29.549549549549553, "y": 24}, {"__type__": "cc.Vec2", "x": -57.65765765765766, "y": 42.66666666666666}, {"__type__": "cc.Vec2", "x": -80, "y": 30}, {"__type__": "cc.Vec2", "x": -73.51351351351352, "y": -42}, {"__type__": "cc.Vec2", "x": -55.4954954954955, "y": -44.666666666666664}, {"__type__": "cc.Vec2", "x": -28.108108108108112, "y": -25.333333333333336}, {"__type__": "cc.Vec2", "x": -0.7207207207207205, "y": -46.666666666666664}, {"__type__": "cc.Vec2", "x": 38.91891891891892, "y": -48.666666666666664}, {"__type__": "cc.Vec2", "x": 72.07207207207207, "y": -26.666666666666668}, {"__type__": "cc.Vec2", "x": 78.55855855855856, "y": 13.333333333333329}, {"__type__": "cc.Vec2", "x": 51.89189189189187, "y": 44.66666666666666}], "_id": "628Uap50dJ5JrJmoMmB78+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 159}, "asset": {"__uuid__": "3ec5473a-3c8c-44d1-a3e7-ed95d95e78ab"}, "fileId": "354S+DCOBOv4Lr0CS7vgxA", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "layerIndex": 4, "_id": "61bQE4L0BM1I5NfrZ1wo+e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 140}, "asset": {"__uuid__": "344c11ad-1c3b-49b3-bb61-ed49b6455f1a"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "lv6_layer3", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 180}], "_active": true, "_components": [{"__id__": 222}], "_prefab": {"__id__": 223}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "60H3o+xNpBeK9iPyJInsz1"}, {"__type__": "cc.Node", "_name": "block60", "_objFlags": 0, "_parent": {"__id__": 179}, "_children": [{"__id__": 181}, {"__id__": 187}, {"__id__": 193}, {"__id__": 199}, {"__id__": 205}, {"__id__": 211}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}, {"__id__": 219}, {"__id__": 220}], "_prefab": {"__id__": 221}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 360}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [140.283, -44.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "daXbAjZWtPgJZ7T/X22C3/"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 182}, {"__id__": 183}, {"__id__": 184}, {"__id__": 185}], "_prefab": {"__id__": 186}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [39.323, 103.495, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "fdqHiVpBJPZqBKI+UhTfDT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b205oSHTJI14S+KKHMy7bT"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "72EhoT1XNJP71EQIdqsUZ/"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "496EqYb8xCArVVBidkFt49"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "pinColor": 1, "_id": "26PC7Bdg9O5LBegG3afSDv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 181}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "07R6tL2S5CU5lBzkl8T8eU", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 189}, {"__id__": 190}, {"__id__": 191}], "_prefab": {"__id__": 192}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.37, 10.151, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "a3KchSANJLFoSPowgsmSbo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3dK0Viid9IlJp9hkWbSaJk"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "48C09ZR4RN1bOdIVjC77oS"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "1fqcT6Nr9AdL340+G3Kxf+"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "pinColor": 1, "_id": "0bQMcA0nlK9IhJvJH2zAmx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 187}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "da4D3gCu1MroJeKl41RcGh", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 194}, {"__id__": 195}, {"__id__": 196}, {"__id__": 197}], "_prefab": {"__id__": 198}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-44.091, 103.495, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "d4yZhm3LtBFbBlF28l2Jn3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c2eHAHWVtCwbcSkjvWYhDD"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "cdkolqfTNGqLIet6FzN5dk"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "52L01l2YZLcZqHuZIFhRBR"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "pinColor": 1, "_id": "2aGPFiVnlHBZ6Chs3ySglh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 193}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "1c20e0ig5LZKNhkOcQRK0Y", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 200}, {"__id__": 201}, {"__id__": 202}, {"__id__": 203}], "_prefab": {"__id__": 204}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-99.7, -13.681, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "62wOi2EGlDw6dx5q23/vao"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "92bVK7vgNEF4Z0cPu/Z3Jh"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "05Ne69tWJDsLdcWaZvoWj1"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "92EeThtNtElJbSoKJulSqH"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "pinColor": 1, "_id": "dae6wz5zxJmKlAS2R3c5+F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 199}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "85aeKrnFdLzIWWHAIPxhMn", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 206}, {"__id__": 207}, {"__id__": 208}, {"__id__": 209}], "_prefab": {"__id__": 210}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [85.001, -11.695, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "c46EXB8XdLuIeHEfD5n5jA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "53sDvdLORPOKtDtuag6ec7"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "canqRbkadFpbziXAT5JeAz"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "333rUY8MxL9LDrYNjG47Rl"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "pinColor": 1, "_id": "16rSnPgNVLq5vzT5SZBlAA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 205}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "b5NMqyJoFHwby6S3GsQdD+", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 212}, {"__id__": 213}, {"__id__": 214}, {"__id__": 215}], "_prefab": {"__id__": 216}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.384, -107.025, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "cf+efJKm9LcJ3AmypaAGK+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 211}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "da3rYKbttMMKr3ayEx66Uz"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 211}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "d0UnQdhidLs6/EEpQMJsCx"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 211}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "c4WsHIjbdCmYPnwyBCtHhM"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 211}, "_enabled": true, "pinColor": 1, "_id": "7b3U2j1fZHpodVHmvuEyvP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 211}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "86AnSGdfxHg4aJe7otkiCL", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c1aed049-929f-4e65-b239-219876023f81"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "30fkIaEpZP47fM0qzTDANP"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 60, "_id": "efexcirD5DorOAVGA7x0v+"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "d2JKRgp4VGp6IiY2KPRG7U"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -9.782608695652186, "y": 180}, {"__type__": "cc.Vec2", "x": -76.6304347826087, "y": 140.83682008368197}, {"__type__": "cc.Vec2", "x": -132.06521739130434, "y": 45.94142259414224}, {"__type__": "cc.Vec2", "x": -150, "y": -56.485355648535574}, {"__type__": "cc.Vec2", "x": -128.80434782608697, "y": -115.23012552301256}, {"__type__": "cc.Vec2", "x": -58.69565217391305, "y": -170.96234309623432}, {"__type__": "cc.Vec2", "x": 22.82608695652172, "y": -180}, {"__type__": "cc.Vec2", "x": 94.56521739130434, "y": -151.3807531380753}, {"__type__": "cc.Vec2", "x": 150, "y": -67.02928870292888}, {"__type__": "cc.Vec2", "x": 140.2173913043478, "y": 21.8410041841004}, {"__type__": "cc.Vec2", "x": 97.82608695652172, "y": 113.7238493723849}, {"__type__": "cc.Vec2", "x": 48.913043478260846, "y": 164.93723849372384}], "_id": "09Ev0mCMhL+orZVh7MW/vo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 180}, "asset": {"__uuid__": "debe1747-7071-43ad-b995-8d81f777d102"}, "fileId": "a40ei8FKdN4at/JoEv8O3J", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "layerIndex": 3, "_id": "70zCx3gghNZqiITBGZpIsM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 179}, "asset": {"__uuid__": "20ebb736-cab5-4a74-aafa-733de8931058"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "lv6_layer2", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 225}], "_active": true, "_components": [{"__id__": 243}], "_prefab": {"__id__": 244}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1b3uS8LMhM/aVu+qYLQykf"}, {"__type__": "cc.Node", "_name": "block15", "_objFlags": 0, "_parent": {"__id__": 224}, "_children": [{"__id__": 226}, {"__id__": 232}], "_active": true, "_components": [{"__id__": 238}, {"__id__": 239}, {"__id__": 240}, {"__id__": 241}], "_prefab": {"__id__": 242}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 192}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [134.325, 176.406, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bADsp2SRBCKY1WjP0P2w2"}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 227}, {"__id__": 228}, {"__id__": 229}, {"__id__": 230}], "_prefab": {"__id__": 231}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [47.267, 2.207, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "47E7ltCeBF0pXXtwWT7max"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "cbvPZs16BOfJJdLfPOQVIa"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "b83nmSTjpBpapsmwDislc1"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "7ax4B6S81OjblJuYmycdiO"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "pinColor": 1, "_id": "381BxcP/JLPZ1Im8LxXl6y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 226}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "07R6tL2S5CU5lBzkl8T8eU", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 234}, {"__id__": 235}, {"__id__": 236}], "_prefab": {"__id__": 237}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-44.091, 4.193, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "1crbczW0hA8bwoPxrvbSKW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a0OSBJ+XRD/pDHBlw+ZNYd"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "cfk8EFRgZD3IBr5AeOk/w7"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "729cSMg1JMaqxE8oo1NT8B"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "pinColor": 1, "_id": "dfk1XkkOFCfJPTTFJALRTL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 232}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "1c20e0ig5LZKNhkOcQRK0Y", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7dc82692-ebb4-4289-8d83-cbdebd30de02"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4b0lBgFMNFAofigYLsFM7+"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 15, "_id": "d6RW7iKopMGrVrWoUnwsyB"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "08GRj33VVBTKUCGOF1kEyf"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -10, "y": 96}, {"__type__": "cc.Vec2", "x": -60, "y": 76}, {"__type__": "cc.Vec2", "x": -91, "y": 33}, {"__type__": "cc.Vec2", "x": -95, "y": -18}, {"__type__": "cc.Vec2", "x": -70, "y": -67}, {"__type__": "cc.Vec2", "x": -18, "y": -95}, {"__type__": "cc.Vec2", "x": 33, "y": -91}, {"__type__": "cc.Vec2", "x": 76, "y": -60}, {"__type__": "cc.Vec2", "x": 96, "y": -10}, {"__type__": "cc.Vec2", "x": 91, "y": 33}, {"__type__": "cc.Vec2", "x": 70, "y": 67}, {"__type__": "cc.Vec2", "x": 33, "y": 91}], "_id": "7c/iCJDK1IXb3fH6vlZu3I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 225}, "asset": {"__uuid__": "debe1747-7071-43ad-b995-8d81f777d102"}, "fileId": "2e9WPU7AVK/Z+EALzf0NGg", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "layerIndex": 2, "_id": "58XwB03wxPdbuhs0XQvAV8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 224}, "asset": {"__uuid__": "ca197a15-ff01-42c5-9c04-0277941bc512"}, "fileId": "", "sync": false}, {"__type__": "cc.Node", "_name": "lv6_layer1", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 246}], "_active": true, "_components": [{"__id__": 288}], "_prefab": {"__id__": 289}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.611, -414.595, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cclknDfMVDna2u3xBkuYup"}, {"__type__": "cc.Node", "_name": "block87", "_objFlags": 0, "_parent": {"__id__": 245}, "_children": [{"__id__": 247}, {"__id__": 253}, {"__id__": 259}, {"__id__": 265}, {"__id__": 271}, {"__id__": 277}], "_active": true, "_components": [{"__id__": 283}, {"__id__": 284}, {"__id__": 285}, {"__id__": 286}], "_prefab": {"__id__": 287}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [137.579, 361.379, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "63a8yi9wdOlbLEpvfTHhK2"}, {"__type__": "cc.Node", "_name": "pin", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 249}, {"__id__": 250}, {"__id__": 251}], "_prefab": {"__id__": 252}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.666, 69.459, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "b8folkSZtC0qEtMPwlqN/H"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "daFj2S0CxDco7J+v2n8J9v"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "6bRAErhL1GULIw7hF3wC5+"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "be7T6WIIBL87d8uGml7ODH"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "pinColor": 1, "_id": "8dztULuXBGRa1N/cQIDPgU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 247}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "a1h2x1XEVPbrrcDbKFPIDM", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 254}, {"__id__": 255}, {"__id__": 256}, {"__id__": 257}], "_prefab": {"__id__": 258}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75.789, 3.92, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "ecIY2l0cROvqwdyQVbjEYg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bfn0NYkaJEOJmAA0YtcFJe"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "7fkcJXfd5PSJ1CiWdXv0m6"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "91RmjE3jdG0qfHEp/TsVPj"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 253}, "_enabled": true, "pinColor": 1, "_id": "27vBVM6IFGrYEY1Zz8ANq4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 253}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "057cL9ciVFjaLY9a7f4ljH", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 260}, {"__id__": 261}, {"__id__": 262}, {"__id__": 263}], "_prefab": {"__id__": 264}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.666, -12.925999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "56xX98RWFGK7+Z+Y/zcilz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "21ZkwxSX9PuYMeNQm3wuky"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "69zHRyy/tIvr1GPLP9fNPL"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "f0mTjECEREw5x704ZDNExA"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "pinColor": 1, "_id": "5etPyYeaBHGbfUu+y4Gydh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 259}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "2bciwXZftHI4mUhX8Kz3Kp", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 266}, {"__id__": 267}, {"__id__": 268}, {"__id__": 269}], "_prefab": {"__id__": 270}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-79.121, 7.892, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "66EKGQE0xJ1ZDtF2vriuOz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "fcBARLHI1BzbRv5QbnOsZt"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "39D8jtVpBIkq0B+x7p5ork"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "22lxnkjpxFXYiDaOYno+eo"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "pinColor": 1, "_id": "fdzCbHPrRPso4le/69of8F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 265}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "26hHRUoBRDr7gk9njMBdE+", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}, {"__id__": 274}, {"__id__": 275}], "_prefab": {"__id__": 276}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-31.456, -81.479, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "88iLgcRixAB6j1DCcUOsic"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d5LGFI8gVPeIEm1lr7Y4M/"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "70U0OP51dE+7zYdSzv83mY"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "729h5a0ZxAaJm50dohMyaj"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "pinColor": 1, "_id": "ecdsN4WuNLRp5F2l/8tOpo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 271}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "dehM3c/QlI27WFnHPq/yMS", "sync": false}, {"__type__": "cc.Node", "_name": "pin copy", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 278}, {"__id__": 279}, {"__id__": 280}, {"__id__": 281}], "_prefab": {"__id__": 282}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [38.055, -79.493, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": "81ATi7JppDv7ZuwrsZiqN6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5e4df079-835f-4d38-a202-55cb2583ab3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bereACBghDy5EWK9anIPwY"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "2fWbeUsoFBS6i6IvZj8NA8"}, {"__type__": "cc.PhysicsCircleCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_radius": 15, "_id": "26oKifk2JMLolSzN32+IeD"}, {"__type__": "57425Gvv5BFka+ourmyy4Hv", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "pinColor": 1, "_id": "684vwpn7dE3bQkh2LGpMQd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 277}, "asset": {"__uuid__": "9c9eb4b2-4f49-4fe2-9d93-1fe9e1c152df"}, "fileId": "eeL+j8ceVN/pBakfgHTc+H", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "_materials": [{"__uuid__": "74e88d6c-23e5-43c2-9756-6428bd7b299d"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ce0f1beb-de57-47f5-a358-64b03aee0ab6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "cfIEVEyY1G9r0bbxsu+77y"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_id": "dbaBtnPqhG9Jfs/ScRBEh3"}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -3.142857142857139, "y": 110}, {"__type__": "cc.Vec2", "x": -35.2, "y": 96.29283489096571}, {"__type__": "cc.Vec2", "x": -53.42857142857143, "y": 64.7663551401869}, {"__type__": "cc.Vec2", "x": -91.77142857142857, "y": 50.37383177570092}, {"__type__": "cc.Vec2", "x": -108.11428571428571, "y": 22.959501557632393}, {"__type__": "cc.Vec2", "x": -102.45714285714286, "y": -25.700934579439263}, {"__type__": "cc.Vec2", "x": -69.14285714285714, "y": -52.42990654205608}, {"__type__": "cc.Vec2", "x": -64.74285714285715, "y": -108.62928348909658}, {"__type__": "cc.Vec2", "x": 64.1142857142857, "y": -108.62928348909658}, {"__type__": "cc.Vec2", "x": 68.5142857142857, "y": -53.11526479750779}, {"__type__": "cc.Vec2", "x": 98.05714285714285, "y": -31.869158878504678}, {"__type__": "cc.Vec2", "x": 110, "y": 3.0841121495327}, {"__type__": "cc.Vec2", "x": 106.22857142857143, "y": 27.757009345794387}, {"__type__": "cc.Vec2", "x": 89.88571428571427, "y": 51.744548286604356}, {"__type__": "cc.Vec2", "x": 53.428571428571416, "y": 64.7663551401869}, {"__type__": "cc.Vec2", "x": 28.91428571428571, "y": 101.0903426791277}], "_id": "b9wZ+s9XVE/YDUMdZ2uGi/"}, {"__type__": "29deeME+NxNHa+v4Yer76av", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "shapeType": 1, "oriSize": null, "shape": 87, "_id": "cbaA/iiNRPN4j8+Kyj7tbo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 246}, "asset": {"__uuid__": "5f7c6bc5-d9a1-4bf1-9f39-56d850d67f44"}, "fileId": "38bWJcChNAo4NYcp/o8VNf", "sync": false}, {"__type__": "f3f738UNhdMg4HWBt+CED8Q", "_name": "", "_objFlags": 0, "node": {"__id__": 245}, "_enabled": true, "layerIndex": 1, "_id": "6e9SPFlCBJRKAzGkiHm0YL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 245}, "asset": {"__uuid__": "ca5fe218-dc1a-4ad5-9676-97d391610161"}, "fileId": "", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_fitWidth": false, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}, {"__type__": "506cesGPnxPCoSTTgpqe32G", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "projectPath": "/Users/<USER>/Documents/Flat/game_c_colorsort/assets/", "configPath": "bundles/config/level/", "exportBtn": {"__id__": 80}, "importBtn": {"__id__": 87}, "exportLevel": 6, "poleCount": 2, "tsaSize": 5, "pinCountLabel": {"__id__": 96}, "updateBtn": {"__id__": 94}, "minLayerNumber": 2, "maxLayerNumber": 3, "root": {"__id__": 100}, "_id": "63ZNwgh5tMMpSgtHUB8ULq"}, {"__type__": "b8ee4CWH7ZJQLyvDC8ntO7A", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "gameUIPrfab": null, "circleFregmentCount": 8, "circleRadius": 28, "displayLayerCount": 2, "_id": "09M/DwWg1FXpWgyUK10Bzb"}]