cd "$(dirname "$0")" || exit

cd release/

rm -rf ./aab_unpack
rm ./BUILD_REPACK.aab
rm ./colorsort-release.aab

unzip game-release.aab -d ./aab_unpack

cd aab_unpack/base

zip -r --no-dir-entries ../../base.zip *
cd ../../ 
java -jar ../bundletool.jar build-bundle --modules=base.zip --config=../config.json --output=BUILD_REPACK.aab

jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore ../colorsort.jks -signedjar colorsort-release.aab BUILD_REPACK.aab colorsort