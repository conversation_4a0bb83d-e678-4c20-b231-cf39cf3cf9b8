package org.cocos2dx.javascript.customSdk;

import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.os.Build;
import android.util.DisplayMetrics;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import io.sentry.android.event.helper.AndroidEventBuilderHelper;

public class SelfEventBuilderHelper extends AndroidEventBuilderHelper {

    private Context ctx;

    public SelfEventBuilderHelper(Context ctx) {
        super(ctx);
        this.ctx = ctx;
    }

    protected Map<String, Map<String, Object>> getContexts() {
        Map<String, Map<String, Object>> contexts = new HashMap<>();
        Map<String, Object> deviceMap = new HashMap<>();
        Map<String, Object> osMap = new HashMap<>();
        Map<String, Object> appMap = new HashMap<>();
        Map<String, Object> CustomMap = new HashMap<>();
        contexts.put("os", osMap);
        contexts.put("device", deviceMap);
        contexts.put("app", appMap);
        contexts.put("custom", CustomMap);

        // Device
        deviceMap.put("manufacturer", Build.MANUFACTURER);//产品/硬件的制造商
        deviceMap.put("brand", Build.BRAND);//与产品/硬件相关联的消费者可见的品牌(如果有的话)
        deviceMap.put("model", Build.MODEL);//最终产品的最终用户可见名称
        deviceMap.put("family", getFamily());//"Nexus 6P" -> "Nexus" 返回设备的姓
        deviceMap.put("model_id", Build.ID);//可以是变更列表号，也可以是“M4-rc20”这样的标签
        deviceMap.put("battery_level", getBatteryLevel(ctx));//获取设备当前电池电量(占总电量的百分比)如果未知则为空
        deviceMap.put("orientation", getOrientation(ctx));//获取设备当前的屏幕方向 如果未知则为空
        deviceMap.put("simulator", isEmulator());//是否是模拟器
        deviceMap.put("storage_size", getTotalInternalStorage());//获取内部存储的总量，以字节为单位
        deviceMap.put("free_storage", getUnusedInternalStorage());//获取未使用的内部存储数量，以字节为单位
        deviceMap.put("external_storage_size", getTotalExternalStorage());//获取外部存储的总量，以字节为单位，如果没有外部存储，则为null
        deviceMap.put("external_free_storage", getUnusedExternalStorage());//获取未使用外部存储的总量，以字节为单位，如果没有外部存储，则为null
        deviceMap.put("charging", isCharging(ctx));//检查设备当前是否处于充电状态，如果未知则为空
        deviceMap.put("online", isConnected(ctx));//是否具有internet访问

        //当前屏幕尺寸 density dpi
        DisplayMetrics displayMetrics = getDisplayMetrics(ctx);
        if (displayMetrics != null) {
            int largestSide = Math.max(displayMetrics.widthPixels, displayMetrics.heightPixels);
            int smallestSide = Math.min(displayMetrics.widthPixels, displayMetrics.heightPixels);
            String resolution = Integer.toString(largestSide) + "x" + Integer.toString(smallestSide);
            deviceMap.put("screen_resolution", resolution);
            deviceMap.put("screen_density", displayMetrics.density);
            deviceMap.put("screen_dpi", displayMetrics.densityDpi);
        }

        //当前内存信息
        ActivityManager.MemoryInfo memInfo = getMemInfo(ctx);
        if (memInfo != null) {
            deviceMap.put("free_memory", memInfo.availMem);
            deviceMap.put("memory_size", memInfo.totalMem);
            deviceMap.put("low_memory", memInfo.lowMemory);
        }

        // Operating System 操作系统相关
        osMap.put("name", "Android");
        osMap.put("version", Build.VERSION.RELEASE);//用户可见的版本字符串
        osMap.put("build", Build.DISPLAY);//用于向用户显示的构建ID字符串
        osMap.put("kernel_version", getKernelVersion());//获取设备的当前内核版本
        osMap.put("rooted", isRooted());//是否root

        // App
        PackageInfo packageInfo = getPackageInfo(ctx);
        if (packageInfo != null) {
            appMap.put("app_version", packageInfo.versionName);
            appMap.put("app_build", packageInfo.versionCode);
            appMap.put("app_identifier", packageInfo.packageName);
        }

        appMap.put("app_name", getApplicationName(ctx));
        appMap.put("app_start_time", stringifyDate(new Date()));
        return contexts;
    }
}
