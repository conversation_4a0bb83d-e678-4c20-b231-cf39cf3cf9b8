package org.cocos2dx.javascript.customSdk;

import static org.cocos2dx.javascript.customSdk.sdkManager.adTime;
import static org.cocos2dx.javascript.customSdk.sdkManager.runEavlString;
import static org.cocos2dx.javascript.customSdk.sdkManager.sendAdjustEvenCC;

import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.applovin.mediation.MaxAd;
import com.applovin.mediation.MaxAdRevenueListener;
import com.applovin.mediation.MaxAdViewAdListener;
import com.applovin.mediation.MaxError;
import com.applovin.mediation.ads.MaxAdView;
import com.applovin.sdk.AppLovinSdk;
import com.applovin.sdk.AppLovinSdkUtils;
import com.google.firebase.analytics.FirebaseAnalytics;

import org.cocos2dx.javascript.AppActivity;
import org.cocos2dx.lib.Cocos2dxActivity;
import org.json.JSONException;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class BannerAdManager extends Cocos2dxActivity
        implements MaxAdViewAdListener, MaxAdRevenueListener{
    public static AppActivity m_pkActivity;
    private static BannerAdManager _instance = null;
    private static AppLovinSdk sdk = null;
    private static Map<String, String> unitIdMap = new HashMap<String, String>();
    private static Map<String, String> unitNameMap = new HashMap<String, String>();
    private static Map<String, MaxAd> adLoadMap = new HashMap<String, MaxAd>();
    private static Map<String, String> adLoadingMap = new HashMap<String, String>();
    private static Map<String, Long> startTimeMap = new HashMap<String, Long>();
    private static ViewGroup rootView;


    private static MaxAdView bannerAd = null;
    private static Map<String, Integer> adRetry = new HashMap<String, Integer>();
    private static int maxReloadTimes = 3;

    public static String bannerId = "b5cfc50845b8cd95";
    public String isNew = "1";

    @Override
    public void onAdExpanded(@NonNull MaxAd maxAd) {
        Log.d("maxAd", "show");
    }

    @Override
    public void onAdCollapsed(@NonNull MaxAd maxAd) {
        Log.d("maxAd", "hide");
    }

    @Override
    public void onAdRevenuePaid(@NonNull MaxAd maxAd) {

    }

    enum AdStatus {
        LoadFailed, // 加载失败
        LoadSuccess, // 加载成功
        ShowFailed, // 显示失败
        ShowSuccess, // 显示成功
        Click, // 点击广告
        Close, // 关闭广告
        RewardUser, // 奖励用户
        RevenuePaid, // 广告收入回调
    }



    public static BannerAdManager getInstance() {
        if (_instance == null) {
            _instance = new BannerAdManager();
        }
        return _instance;
    }

    public void init(AppActivity pActivity, AppLovinSdk maxSdk){
        this.unitIdMap.put("banner_1", "b5cfc50845b8cd95");
        this.unitNameMap.put(this.unitIdMap.get("banner_1"), "banner_1");
        this.adLoadMap.put(this.unitIdMap.get("banner_1"),null);
        adRetry.put(this.unitIdMap.get("banner_1"), 0);
        m_pkActivity = pActivity;
        sdk = maxSdk;
        //createBanner();
    }

    //================Ads============
    static void createBanner() {

        Log.d("maxAd", "createAd");
        bannerAd = new MaxAdView(unitIdMap.get("banner_1"), sdk, m_pkActivity);
        bannerAd.setListener(BannerAdManager.getInstance());
        bannerAd.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, "Banner");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev_gg",params1);
            }
        });
        DisplayMetrics dm = new DisplayMetrics();
        m_pkActivity.getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = ViewGroup.LayoutParams.MATCH_PARENT* dm.densityDpi/320;
        int heightPx = 110* dm.densityDpi/320;
        Log.d("maxAd", width+"-----"+heightPx);

        bannerAd.setLayoutParams( new FrameLayout.LayoutParams(width, heightPx, Gravity.BOTTOM+10));
        bannerAd.setExtraParameter( "adaptive_banner", "true" );
        bannerAd.setLocalExtraParameter( "adaptive_banner_width", 400 );
        bannerAd.setBackgroundColor(1);
        bannerAd.getAdFormat().getAdaptiveSize( 400, m_pkActivity ).getHeight(); // Set your ad height to this value
        bannerAd.setExtraParameter( "allow_pause_auto_refresh_immediately", "true" );
        //AppActivity.addBannerView(bannerAd,  new FrameLayout.LayoutParams( width, heightPx ));
        rootView = m_pkActivity.findViewById(android.R.id.content);
        rootView.addView(bannerAd);
        adLoadingMap.put(bannerId,"0");
        adLoadMap.put(bannerId,null);
        reloadAd(false);
        bannerAd.startAutoRefresh();
    }


    // MAX Ad Listener
    @Override
    public void onAdLoaded(final MaxAd maxAd) {
        AppLovinSdkUtils.Size adViewSize = maxAd.getSize();
        int widthDp = adViewSize.getWidth();
        int heightDp = adViewSize.getHeight();
        // Rewarded ad is ready to be shown. rewardedAd.isReady() will now return 'true'
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, 2 , maxAd.getPlacement(), AdStatus.LoadSuccess.ordinal());
        runEavlString(evalString);
        adLoadingMap.put(maxAd.getAdUnitId(),"0");
        this.adLoadMap.put(maxAd.getAdUnitId(), maxAd);
        startTimeMap.put(bannerId, System.currentTimeMillis());
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Banner");
            jsonObj1.put("placement_id", this.unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            jsonObj1.put("revenue", new BigDecimal(maxAd.getRevenue()).toPlainString());
            jsonObj1.put("currency", "USD");
            jsonObj1.put("precision",maxAd.getRevenuePrecision());
            jsonObj1.put("cost",now - startTimeMap.get(maxAd.getAdUnitId()));
            sendAdjustEvenCC("ad_fill_suc", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        // Reset retry attempt
        adRetry.put(maxAd.getAdUnitId(),0);
    }

    @Override
    public void onAdLoadFailed(final String adUnitId, final MaxError error) {
        // Rewarded ad failed to load
        // We recommend retrying with exponentially higher delays up to a maximum delay
        // (in this case 64 seconds)
        // (in this case 64 seconds)
        Log.d("maxAd", "loadFail");
        this.adLoadMap.put(adUnitId,null);
        int retryAttempt = adRetry.get(adUnitId)+1;
        adRetry.put(adUnitId,retryAttempt);
        long delayMillis = TimeUnit.SECONDS.toMillis((long) Math.pow(2, Math.min(3, retryAttempt)));
//        if(retryAttempt <= maxReloadTimes) {
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    reloadAd(false);
//                }
//            }, delayMillis );
//        }
        Log.d("maxAd", adUnitId + error.toString());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, 0, adUnitId, AdStatus.LoadFailed.ordinal());
        runEavlString(evalString);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Banner");
            jsonObj1.put("placement_id", this.unitNameMap.get(adUnitId));
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", adUnitId);
            jsonObj1.put("error", error.getCode()+"");
            String msg = error.getMessage().replace('"', ' ');
            jsonObj1.put("msg",  msg);
            jsonObj1.put("cost",now - startTimeMap.get(adUnitId));
            sendAdjustEvenCC("ad_fill_fail", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        startTimeMap.put(adUnitId, System.currentTimeMillis());
    }

    public static void reloadAd(boolean retry) {
        if(adLoadingMap.get(bannerId) == "1")return;

        if(adLoadMap.get(bannerId) == null) {
            if(!retry){
                startTimeMap.put(bannerId, System.currentTimeMillis());
            }
            adLoadingMap.put(bannerId, "1");
            if (unitNameMap.get(bannerId).indexOf("1") > -1) {
                bannerAd.loadAd();
            }

                try {
                    JSONObject jsonObj1 = new JSONObject("{}");
                    jsonObj1.put("format", "Banner");
                    jsonObj1.put("placement_id", unitNameMap.get(bannerId));
                    jsonObj1.put("ad_platform", "applovin");
                    jsonObj1.put("unitid", bannerId);
                    sendAdjustEvenCC("ad_pull", jsonObj1.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }

//                }else if(unitNameMap.get(adUnitId).indexOf("2") > -1) {
//                    instialAd1.loadAd();
//                } else {
//                    instialAd2.loadAd();
//                }
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if(adLoadingMap.get(bannerId) == "1") {
                        adLoadingMap.put(bannerId, "0");
                        adLoadMap.put(bannerId, null);
                        Log.d("addLogPrint", unitNameMap.get(bannerId)+"广告加载超时，重新加载");
                        reloadAd(true);
                    }
                }


            }, adTime );

        }
    }

    public static void showBanner(final String placementName){
        BannerAdManager.getInstance().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Log.d("maxad", "show");
                if(bannerAd != null){
                    bannerAd.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    public static void hideBanner(final String placementName){
        BannerAdManager.getInstance().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Log.d("maxad", "hide");
                if(bannerAd != null){
                    bannerAd.setVisibility(View.INVISIBLE);
                }
            }
        });
    }

    @Override
    public void onAdDisplayFailed(final MaxAd maxAd, final MaxError error) {

    }

    @Override
    public void onAdDisplayed(final MaxAd maxAd) {

    }

    @Override
    public void onAdClicked(final MaxAd maxAd) {
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, 2 , maxAd.getPlacement(), AdStatus.Click.ordinal());
        runEavlString(evalString);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Banner");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            sendAdjustEvenCC("ad_click", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onAdHidden(final MaxAd maxAd) {
        // rewarded ad is hidden. Pre-load the next ad
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat,2, maxAd.getPlacement(), AdStatus.Close.ordinal());
        runEavlString(evalString);
        adLoadMap.put(maxAd.getAdUnitId(), null);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Banner");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            sendAdjustEvenCC("ad_close", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        //reloadAd(false);
    }

}
