package org.cocos2dx.javascript.customSdk;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.StyleSpan;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import org.cocos2dx.javascript.AppActivity;
import org.cocos2dx.javascript.R;
import org.json.JSONObject;

import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class NotificationUtil {
    private static final String TAG = "NotificationUtil";

    /**
     * 通过定时闹钟发送通知
     */
    public static void notifyByAlarm(Context context, Map<Integer, NotifyObject> notifyObjectMap) {
        //将数据存储起来
        int count = 0;
        Set<Integer> keySet = notifyObjectMap.keySet();
        for (Integer key0 : keySet) {
            if (!notifyObjectMap.containsKey(key0)) {
                break;
            }

            NotifyObject obj = notifyObjectMap.get(key0);
            if (obj == null) {
                break;
            }

            if (obj.times.size() == 0) {
                if (obj.firstTime > 0) {
                    try {
                        Map<String, Serializable> map = new HashMap<>();
                        map.put("KEY_NOTIFY_ID", obj.type);
                        map.put("KEY_NOTIFY", NotifyObject.to(obj));
                        AlarmTimerUtil.setAlarmTimer(context, ++count, obj.firstTime, "TIMER_ACTION", map);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                for (long time : obj.times) {
                    if (time > 0) {
                        try {
                            Map<String, Serializable> map = new HashMap<>();
                            map.put("KEY_NOTIFY_ID", obj.type);
                            map.put("KEY_NOTIFY", NotifyObject.to(obj));
                            AlarmTimerUtil.setAlarmTimer(context, ++count, time, "TIMER_ACTION", map);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }

        SharedPreferences mPreferences = context.getSharedPreferences("SHARE_PREFERENCE_NOTIFICATION", Context.MODE_PRIVATE);
        SharedPreferences.Editor edit = mPreferences.edit();
        edit.putInt("KEY_MAX_ALARM_ID", count);
        edit.apply();
    }


    public static void notifyByAlarmByReceiver(Context context, NotifyObject obj) {
        if (context == null || obj == null) return;
        sendNotification(context, obj.pushId, obj.title, obj.content);
    }

    private static void sendNotification(Context context, String pushId, String title, String messageBody) {
        Intent intent = new Intent(context, AppActivity.class);
        intent.putExtra("from_top", pushId);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        String channelId = "notify_channel_1";
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        SpannableString t = new SpannableString(title);
        t.setSpan(new StyleSpan(Typeface.BOLD), 0, t.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(context, channelId)
                        .setSmallIcon(R.mipmap.ic_launcher)
                        .setContentTitle(t)
                        .setContentText(messageBody)
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent);

        try {
            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel(channelId,
                        "Channel human readable title",
                        NotificationManager.IMPORTANCE_DEFAULT);
                notificationManager.createNotificationChannel(channel);
            }

            notificationManager.notify(0 /* ID of notification */, notificationBuilder.build());

            JSONObject jsonObj1 = new JSONObject();
//            jsonObj1.put("pushID", pushId);
//            sdkManager.sendAdjustEvenAn("push", jsonObj1.toString());

        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
    }

    /**
     * 取消所有通知 同时取消定时闹钟
     *
     * @param context
     */
    public static void clearAllNotifyMsg(Context context) {
        try {
            NotificationManager mNotifyMgr = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            mNotifyMgr.cancelAll();

            SharedPreferences mPreferences = context.getSharedPreferences("SHARE_PREFERENCE_NOTIFICATION", Context.MODE_PRIVATE);
            int max_id = mPreferences.getInt("KEY_MAX_ALARM_ID", 0);
            for (int i = 1; i <= max_id; i++) {
                AlarmTimerUtil.cancelAlarmTimer(context, "TIMER_ACTION", i);
            }
            //清除数据
            mPreferences.edit().remove("KEY_MAX_ALARM_ID").apply();

        } catch (Exception e) {
            Log.e(TAG, "取消通知失败", e);
        }
    }
}
