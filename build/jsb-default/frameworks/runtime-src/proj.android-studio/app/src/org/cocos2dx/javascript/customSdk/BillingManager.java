package org.cocos2dx.javascript.customSdk;


import static org.cocos2dx.javascript.customSdk.sdkManager.sendAdjustEvenCC;

import android.content.Context;
import android.os.Bundle;
import android.util.ArrayMap;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.billingclient.api.AcknowledgePurchaseParams;
import com.android.billingclient.api.AcknowledgePurchaseResponseListener;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ConsumeResponseListener;
import com.android.billingclient.api.PendingPurchasesParams;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.ProductDetailsResponseListener;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesResponseListener;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryPurchasesParams;
import com.google.firebase.analytics.FirebaseAnalytics;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class BillingManager
{
    private ConsumeResponseListener consumeResponseListener;
    private AcknowledgePurchaseResponseListener acknowledgePurchaseResponseListener;

    private static BillingClient billingClient;
    private static List<ProductDetails> skuDetailsList;
    private static String productId;
    private static long startTime;
    private static long productTime;

    static int maxTries = 3;
    static int tries = 1;
    static boolean isConnectionEstablished = false;
    static int errCode = 0;

    private PurchasesResponseListener purchasesResponseListener =new PurchasesResponseListener() {
        @Override
        public void onQueryPurchasesResponse(@NonNull BillingResult billingResult, @NonNull List<Purchase> list) {
            //list为购买交易的集合
        }
    };

    private Context context;
    static ArrayMap<String, String> currencyArrayMap = new ArrayMap<>();

    public BillingManager(Context context) {
        this.context = context;
        //核销回调
        consumeResponseListener=new ConsumeResponseListener() {
            @Override
            public void onConsumeResponse(@NonNull BillingResult billingResult, @NonNull String purchaseToken) {
                //核销完成后回调
                Log.d("product","核销完成回调");
            }
        };

        //核销回调
        acknowledgePurchaseResponseListener=new AcknowledgePurchaseResponseListener() {
            @Override
            public void onAcknowledgePurchaseResponse(@NonNull BillingResult billingResult) {
                //核销完成后回调
            }
        };
        PurchasesUpdatedListener purchasesUpdatedListener = new PurchasesUpdatedListener() {
            @Override
            public void onPurchasesUpdated(@NonNull BillingResult billingResult, @Nullable List<Purchase> purchases) {
                switch (billingResult.getResponseCode()) {
                    case BillingClient.BillingResponseCode.OK:
                        //购买商品成功
                        Log.d("products", "购买成功");
                        for (Purchase purchase : purchases) {
                            if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                                //通过服务器验证订单，此处省略
                                //可重复购买的内购商品核销
                                Log.d("products", purchase.getProducts().toString());
                                String strFormat = "window.App.event.dispatch('BUY_RESULT', {resultCode:%d, products:'%s', order_id:'%s',token:'%s'});";
                                String evalString = String.format(strFormat, billingResult.getResponseCode(), String.join(",",purchase.getProducts()), purchase.getOrderId(), purchase.getPurchaseToken());
                                sdkManager.runEavlString(evalString);
                                consumePurchase(purchase.getPurchaseToken());
                                for(ProductDetails product: skuDetailsList){
                                    if(product.getProductId().equals(String.join(",",purchase.getProducts()))){
                                        try {
                                            sdkManager.sendFbEvent("pur_revenue_fb",Double.valueOf(product.getOneTimePurchaseOfferDetails().getPriceAmountMicros())/1000000, product.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                                            sdkManager.sendFbEvent("all_revenue_fb",Double.valueOf(product.getOneTimePurchaseOfferDetails().getPriceAmountMicros())/1000000,product.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                                        } catch (NoSuchFieldException e) {
                                            throw new RuntimeException(e);
                                        } catch (IllegalAccessException e) {
                                            throw new RuntimeException(e);
                                        }
                                        Bundle params = new Bundle();
                                        params.putDouble(FirebaseAnalytics.Param.SHIPPING, Double.valueOf(product.getOneTimePurchaseOfferDetails().getPriceAmountMicros())/1000000);
                                        params.putString(FirebaseAnalytics.Param.CURRENCY, product.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                                        params.putDouble(FirebaseAnalytics.Param.VALUE, Double.valueOf(product.getOneTimePurchaseOfferDetails().getPriceAmountMicros())/1000000);
                                        params.putString(FirebaseAnalytics.Param.ITEM_ID, product.getProductId());
                                        Log.d("firebase",params.toString());
                                        FirebaseAnalytics.getInstance((context)).logEvent(FirebaseAnalytics.Event.PURCHASE,params);
                                        FirebaseAnalytics.getInstance((context)).logEvent("pur_revenue_gg",params);
                                        FirebaseAnalytics.getInstance((context)).logEvent("all_rev_gg",params);
                                    }
                                }

                                //不可重复购买的内购商品、订阅商品核销
                                //acknowledgedPurchase(purchase.getPurchaseToken());
                            }
                        }
                        break;
                    default:
                        //购买失败，具体异常码可以到BillingClient.BillingResponseCode中查看
                        Log.d("products", "购买失败");
//                        for (Purchase purchase : purchases) {
                        String strFormat = "window.App.event.dispatch('BUY_RESULT', {resultCode:%d, products:'%s'});";
                        String evalString = String.format(strFormat, billingResult.getResponseCode(),productId);
                        sdkManager.runEavlString(evalString);
//                        }
                        break;
                }
            }
        };

        PendingPurchasesParams.Builder var10001 = PendingPurchasesParams.newBuilder();
        var10001.enableOneTimeProducts();
        billingClient = BillingClient.newBuilder(sdkManager.m_pkActivity.getApplication())
                .setListener(purchasesUpdatedListener)
                .enablePendingPurchases(var10001.build())
                .build();

    }

    void consumePurchase(String purchaseToken) {
        Log.d("products", purchaseToken);
        if (billingClient != null && billingClient.isReady()) {
            ConsumeParams consumeParams = ConsumeParams.newBuilder()
                    .setPurchaseToken(purchaseToken)
                    .build();
            billingClient.consumeAsync(consumeParams, consumeResponseListener);
        }
    }

    void acknowledgedPurchase(String purchaseToken) {
        if (billingClient != null && billingClient.isReady()) {
            AcknowledgePurchaseParams acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchaseToken)
                    .build();
            billingClient.acknowledgePurchase(acknowledgePurchaseParams, acknowledgePurchaseResponseListener);
        }
    }


    public static int BuyProducts(final String productId){
        if(!isConnectionEstablished){
            return errCode;
        }
        Log.d("products", productId);
        for (ProductDetails productDetail : skuDetailsList) {
            Log.d("products", productDetail.getProductId());
            if(productDetail.getProductId().equals(productId)){
                ArrayList<BillingFlowParams.ProductDetailsParams> params = new ArrayList<>();
                //将要购买商品的商品详情配置到参数中，两种类型的商品有所区别
                Log.d("products", productDetail.toString());
                params.add(BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetail)
                        .build());


                BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                        .setProductDetailsParamsList(params)
                        .build();
                BillingManager.productId = productId;

                //启动购买，返回BillingResult。
                BillingResult billingResult = BillingManager.billingClient.launchBillingFlow(sdkManager.m_pkActivity, billingFlowParams);
                return billingResult.getResponseCode();
            }
        }
        return -5;
    }

    public void checkByResult(){
        //内购商品交易查询
        QueryPurchasesParams inAppPurchasesQuery = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .build();
        billingClient.queryPurchasesAsync(inAppPurchasesQuery, purchasesResponseListener);
    }

    static String replaceCurrencySymbol(String priceStr, String currencyCode, String currencySymbol) {
        if (priceStr.startsWith("$")) {
            if (currencySymbol != null) {
                if (currencySymbol.equals(currencyCode)) {
                    //没有货币符号的情况，把货币码拼接到前面
                    priceStr = currencySymbol + priceStr;
                } else {
                    if (!priceStr.startsWith(currencySymbol)) {
                        priceStr = priceStr.replace("$", currencySymbol);
                    }
                }
            }
        }
        return priceStr;
    }
    public static String initPaying(){
        Long now = System.currentTimeMillis();
        startTime = now;


            billingClient.startConnection(new BillingClientStateListener() {

                @Override
                public void onBillingServiceDisconnected() {
                    Log.d("addlogP", "disconnect");
                    try {
                        Long now = System.currentTimeMillis();
                        JSONObject jsonObj1 = new JSONObject("{}");
                        sendAdjustEvenCC("pur_disconnect", jsonObj1.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                    retryBillingServiceConnection();
                }

                private void retryBillingServiceConnection() {
                    isConnectionEstablished = false;
                    try{
                        if(tries <= maxTries) {
                            billingClient.startConnection(this);
                            tries = tries +1;
                        }
                    } catch (Exception e){

                    }
                }

           
                @Override
                public void onBillingSetupFinished(@NonNull BillingResult billingResult) {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        isConnectionEstablished = true;
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("duration", now - startTime);
                            sendAdjustEvenCC("initialization_pur_sdk", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                        //支付初始化成功
                        ArrayList<QueryProductDetailsParams.Product> inAppProductInfo = new ArrayList<>();
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_test3_0.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket6_1.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket30_7.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket60_14.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket110_24.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket250_47.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_ticket600_94.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_wood_removeads_4.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_removeads_pack_7.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_removeads_limitedpack_4.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_super_giftpack_9.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        inAppProductInfo.add(QueryProductDetailsParams.Product.newBuilder()
                                .setProductId("com_starter_pack_0.99")
                                .setProductType(BillingClient.ProductType.INAPP)
                                .build());
                        QueryProductDetailsParams productDetailsParams = QueryProductDetailsParams.newBuilder()
                                .setProductList(inAppProductInfo)
                                .build();

                        try {
                            Long now = System.currentTimeMillis();
                            productTime = now;
                            JSONObject jsonObj1 = new JSONObject("{}");
                            sendAdjustEvenCC("initialization_pur_product", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                        billingClient.queryProductDetailsAsync(productDetailsParams, new ProductDetailsResponseListener() {
                            @Override
                            public void onProductDetailsResponse(@NonNull BillingResult billingResult, @NonNull List<ProductDetails> productDetailsList) {
                                //productDetailsList为可用商品的集合
                                skuDetailsList = productDetailsList;
                                Log.d("products", billingResult.toString());
                                Log.d("products", productDetailsList.toString());
                                try {
                                    Long now = System.currentTimeMillis();
                                    JSONObject jsonObj1 = new JSONObject("{}");
                                    jsonObj1.put("duration", now - productTime);
                                    sendAdjustEvenCC("initialization_pur_product_suc", jsonObj1.toString());
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                } catch (NoSuchFieldException e) {
                                    throw new RuntimeException(e);
                                } catch (IllegalAccessException e) {
                                    throw new RuntimeException(e);
                                }
                                for (ProductDetails productDetail : productDetailsList) {
                                    if (BillingClient.ProductType.INAPP.equals(productDetail.getProductType()) && productDetail.getOneTimePurchaseOfferDetails() != null) {
                                        String googleProductPrice = productDetail.getOneTimePurchaseOfferDetails().getFormattedPrice();
                                        String googleCurrencyCode = productDetail.getOneTimePurchaseOfferDetails().getPriceCurrencyCode();
                                        String currencySymbol = currencyArrayMap.get(googleCurrencyCode) == null ? googleCurrencyCode : currencyArrayMap.get(googleCurrencyCode);

                                        String replacePrice = replaceCurrencySymbol(googleProductPrice, googleCurrencyCode, currencySymbol);
                                        String strFo = "window.App.event.dispatch('PRODUCT_DETAILS', {productId:'%s',price:'%s', num:%d, unit:'%s' });";
                                        String evalString = String.format(strFo, productDetail.getProductId(), replacePrice, productDetail.getOneTimePurchaseOfferDetails().getPriceAmountMicros(), productDetail.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                                        sdkManager.runEavlString(evalString);
                                        Log.d("products", evalString);
                                    }
                                }

                            }
                        });
                    } else {
                        retryBillingServiceConnection();
                        errCode = billingResult.getResponseCode();
                        Log.d("addlogP", String.valueOf(billingResult.getResponseCode()));
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("error",billingResult.getResponseCode());
                            jsonObj1.put("msg","内购初始化失败");
                            sendAdjustEvenCC("initialization_pur_fail", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                });
            return "";
    }

    public static void sendAdjustRevenue(final String a_name, String productId)
            throws NoSuchFieldException, IllegalAccessException {
        for (ProductDetails productDetail : skuDetailsList) {
            Log.d("products", productDetail.getProductId());
            if(productDetail.getProductId().equals(productId)) {
                {
                    try {
                        sdkManager.sendAdjustEventRevenue("all_revenue_adj", Double.valueOf(productDetail.getOneTimePurchaseOfferDetails().getPriceAmountMicros() / 1000000), productDetail.getOneTimePurchaseOfferDetails().getPriceCurrencyCode(),"");
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }

                }
            }
        }
    }

}