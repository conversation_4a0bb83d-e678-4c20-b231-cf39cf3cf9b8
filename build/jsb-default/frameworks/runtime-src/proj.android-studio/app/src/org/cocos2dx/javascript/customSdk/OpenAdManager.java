package org.cocos2dx.javascript.customSdk;

import static org.cocos2dx.javascript.customSdk.sdkManager.sendAdjustEvenCC;
import static org.cocos2dx.javascript.customSdk.sdkManager.sendAdjustEvent;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.appopen.AppOpenAd;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class OpenAdManager
        implements DefaultLifecycleObserver
{
    private AppOpenAd appOpenAd = null;
    private boolean isLoadingAd = false;
    private boolean isShowingAd = false;
    private  Context context;

    private static String LOG_TAG = "OpenAd";

    private long loadTime;
    private long startTime;

    private  static String openAdId = "ca-app-pub-7221440554606229/1365349332";
    private static String openName = "open_1";

    /** Constructor. */
    public OpenAdManager(Context context) {
        ProcessLifecycleOwner.get().getLifecycle().addObserver( this );
        this.context = context;
    }

    public void loadAd(Context context) {
        // Do not load ad if there is an unused ad or one is already loading.
        if(sdkManager.openSwitch.equals("0")){
            return;
        }
        if (isLoadingAd || isAdAvailable()) {
            return;
        }
        try {
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format", "OpenAd");
            jsonObj1.put("placement_id", openName);
            jsonObj1.put("ad_platform", "admob");
            jsonObj1.put("unitid", openAdId);
            sendAdjustEvenCC("ad_pull", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        startTime = System.currentTimeMillis();
        isLoadingAd = true;
        AdRequest request = new AdRequest.Builder().build();
        AppOpenAd.load(
                context, openAdId, request,
                new AppOpenAd.AppOpenAdLoadCallback() {
                    @Override
                    public void onAdLoaded(AppOpenAd ad) {
                        // Called when an app open ad has loaded.
                        Log.d(LOG_TAG, "Ad was loaded.");

                        appOpenAd = ad;
                        isLoadingAd = false;
                        loadTime = (new Date()).getTime();
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("format","OpenAd");
                            jsonObj1.put("placement_id", openName);
                            jsonObj1.put("ad_platform", "admob");
                            jsonObj1.put("unitid", openAdId);
                            jsonObj1.put("network", "admob");
                            jsonObj1.put("network_id", openAdId);
                            jsonObj1.put("revenue", "0");
                            jsonObj1.put("currency", "USD");
                            jsonObj1.put("precision","excat");
                            jsonObj1.put("cost",now - startTime);
                            sendAdjustEvenCC("ad_fill_suc", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError loadAdError) {
                        // Called when an app open ad has failed to load.
                        Log.d(LOG_TAG, loadAdError.getMessage());
                        isLoadingAd = false;
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("format","OpenAd");
                            jsonObj1.put("placement_id", openName);
                            jsonObj1.put("ad_platform", "admob");
                            jsonObj1.put("unitid", openAdId);
                            jsonObj1.put("network", "admob");
                            jsonObj1.put("network_id", openAdId);
                            jsonObj1.put("revenue", "0");
                            jsonObj1.put("currency", "USD");
                            jsonObj1.put("precision","excat");
                            jsonObj1.put("cost",now - startTime);
                            sendAdjustEvenCC("ad_fill_fail", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
    }
    // ...

    public interface OnShowAdCompleteListener {
        void onShowAdComplete();
    }


    /** Show the ad if one isn't already showing. */
    private void showAdIfAvailable(@NonNull final Context activity) {
        if(activity == null ) return;
        if(sdkManager.openSwitch.equals("0")){
            return;
        }
        if(sdkManager.showOpen.equals("0")) return;
        if(sdkManager.isAd)return;
        try {
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","OpenAd");
            jsonObj1.put("placement_name",openName);
            sdkManager.sendAdjustEvenCC("ad_show_start", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        showAdIfAvailable(
                activity,
                new OnShowAdCompleteListener() {
                    @Override
                    public void onShowAdComplete() {
                        Log.d(LOG_TAG, "广告播放成功");
                        // Empty because the user will go back to the activity that shows the ad.?
                    }
                });
    }


    /** Shows the ad if one isn't already showing. */
    public void showAdIfAvailable(
            @NonNull final Context activity,
            @NonNull OnShowAdCompleteListener onShowAdCompleteListener){
        // If the app open ad is already showing, do not show the ad again.
        if (isShowingAd) {
            Log.d(LOG_TAG, "The app open ad is already showing.");
            return;
        }

        // If the app open ad is not available yet, invoke the callback then load the ad.
        if (!isAdAvailable()) {
            Log.d(LOG_TAG, "The app open ad is not ready yet.");
            onShowAdCompleteListener.onShowAdComplete();
            loadAd(activity);
            return;
        }

        appOpenAd.setFullScreenContentCallback(
                new FullScreenContentCallback() {

                    @Override
                    public void onAdDismissedFullScreenContent() {
                        // Called when fullscreen content is dismissed.
                        // Set the reference to null so isAdAvailable() returns false.
                        Log.d(LOG_TAG, "Ad dismissed fullscreen content.");
                        appOpenAd = null;
                        isShowingAd = false;

                        onShowAdCompleteListener.onShowAdComplete();
                        loadAd(activity);
                    }

                    @Override
                    public void onAdFailedToShowFullScreenContent(AdError adError) {
                        // Called when fullscreen content failed to show.
                        // Set the reference to null so isAdAvailable() returns false.
                        Log.d(LOG_TAG, adError.getMessage());
                        appOpenAd = null;
                        isShowingAd = false;

                        onShowAdCompleteListener.onShowAdComplete();
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("format","OpenAd");
                            jsonObj1.put("placement_id", openName);
                            jsonObj1.put("ad_platform", "admob");
                            jsonObj1.put("unitid", openAdId);
                            jsonObj1.put("network", "admob");
                            jsonObj1.put("network_id", openAdId);
                            jsonObj1.put("revenue", "0");
                            jsonObj1.put("currency", "USD");
                            jsonObj1.put("precision","excat");
                            jsonObj1.put("cost",now - startTime);
                            sendAdjustEvenCC("ad_show_fail", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                        loadAd(activity);
                    }

                    @Override
                    public void onAdClicked() {
                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("format","OpenAd");
                            jsonObj1.put("placement_id", openName);
                            jsonObj1.put("placement_name",openName);
                            jsonObj1.put("ad_platform", "admob");
                            jsonObj1.put("unitid", openAdId);
                            jsonObj1.put("network", "admob");
                            jsonObj1.put("network_id", openAdId);
                            sdkManager.sendAdjustEvenCC("ad_click", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }


                    @Override
                    public void onAdShowedFullScreenContent() {
                        // Called when fullscreen content is shown.
                        Log.d(LOG_TAG, "Ad showed fullscreen content.");

                        try {
                            Long now = System.currentTimeMillis();
                            JSONObject jsonObj1 = new JSONObject("{}");
                            jsonObj1.put("format","OpenAd");
                            jsonObj1.put("placement_id", openName);
                            jsonObj1.put("ad_platform", "admob");
                            jsonObj1.put("unitid", openAdId);
                            jsonObj1.put("network", "admob");
                            jsonObj1.put("network_id", openAdId);
                            jsonObj1.put("revenue", "0");
                            jsonObj1.put("currency", "USD");
                            jsonObj1.put("precision","excat");
                            jsonObj1.put("cost",now - startTime);
                            sendAdjustEvent("ad_show_suc", jsonObj1.toString());
                            sendAdjustEvenCC("ad_show_suc", jsonObj1.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });

        isShowingAd = true;
        appOpenAd.show(sdkManager.getInstance().m_pkActivity);
    }
    // ...


    /** Utility method to check if ad was loaded more than n hours ago. */
    private boolean wasLoadTimeLessThanNHoursAgo(long numHours) {
        long dateDifference = (new Date()).getTime() - this.loadTime;
        long numMilliSecondsPerHour = 3600000;
        return (dateDifference < (numMilliSecondsPerHour * numHours));
    }

    /** Check if ad exists and can be shown. */
    public boolean isAdAvailable() {
        return appOpenAd != null && wasLoadTimeLessThanNHoursAgo(4);
    }


    @Override
    public void onCreate(@NonNull LifecycleOwner owner) {
        Log.d("oepnAD", "onCreate: owner=" + owner);
        this.showAdIfAvailable(this.context);
    }

    @Override
    public void onResume(@NonNull LifecycleOwner owner) {
        Log.d("oepnAD", "onResume: owner=" + owner);
        this.showAdIfAvailable(this.context);
    }

}