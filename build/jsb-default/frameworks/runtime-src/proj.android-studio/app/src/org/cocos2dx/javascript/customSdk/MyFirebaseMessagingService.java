package org.cocos2dx.javascript.customSdk;

import android.app.ActivityManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.StyleSpan;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.google.android.gms.common.util.Strings;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import org.cocos2dx.javascript.AppActivity;
import org.cocos2dx.javascript.R;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Locale;
import java.util.Random;

public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "FirebaseMsgService";

    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        if (!isBackgroundProcess(AppActivity.getContext())) {
            return;
        }
        if (remoteMessage.getNotification() != null) {
            String notificationBody = remoteMessage.getNotification().getBody();
            if (notificationBody != null && !notificationBody.isEmpty()) {
                sendNotification(remoteMessage.getNotification().getTitle(), notificationBody);
            }
        }
    }

    public static boolean isBackgroundProcess(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        boolean isBackground = true;
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(context.getPackageName())) {
                if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_CACHED) {
                    isBackground = true;
                } else if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                        || appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE) {
                    isBackground = false;
                } else {
                    isBackground = true;
                }
            }
        }
        return isBackground;
    }

    @Override
    public void handleIntent(Intent intent) {
        String body = intent.getStringExtra("gcm.notification.body");
        String title = intent.getStringExtra("gcm.notification.title");
        String pushId = intent.getStringExtra("gcm.message.push.id");
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

        if (title == null || body == null) return;

        if (title.equals("") || title.equals("Rewards available") || title.equals("Leaderboards")) {  // 兼容老版本
            Random r = new Random();
            r.setSeed(System.currentTimeMillis());
            int idx = r.nextInt(2);
            if (idx == 0) {
                pushId = "PushTitle01";
            } else {
                pushId = "PushTitle02";
            }
            String language = Locale.getDefault().getLanguage();
            switch (language) {
                case "hi": {
                    String[] titleArr = {"पुरस्कार उपलब्ध", "लीडरबोर्ड"};
                    title = titleArr[idx];
                    String[] bodyArr = {"🎁 कार्यों में भाग लें और आइटम पुरस्कार प्राप्त करें >>>", "🏆 अन्य खिलाड़ियों को हराने के लिए चुनौती स्तर >>>"};
                    body = bodyArr[idx];
                    break;
                }
                case "in": {
                    String[] titleArr = {"Hadiah tersedia", "Papan Peringkat"};
                    title = titleArr[idx];

                    String[] bodyArr = {"🎁 Berpartisipasilah dalam tugas dan terima hadiah item >>>", "🏆 Tantangan level untuk mengalahkan pemain lain >>>"};
                    body = bodyArr[idx];
                    break;
                }
                case "pt": {
                    String[] titleArr = {"Recompensas disponíveis", "Placares"};
                    title = titleArr[idx];

                    String[] bodyArr = {"🎁 Participe de tarefas e receba recompensas de itens >>>", "🏆 Desafie níveis para derrotar outros jogadores >>>"};
                    body = bodyArr[idx];
                    break;
                }
                default: {
                    String[] titleArr = {"Rewards available", "Leaderboards"};
                    title = titleArr[idx];

                    String[] bodyArr = {"🎁 Participate in tasks and receive item rewards >>>", "🏆 Challenge levels to defeat other players >>>"};
                    body = bodyArr[idx];
                    break;
                }
            }
        }
        intent.putExtra("gcm.notification.body", body);
        intent.putExtra("gcm.notification.title", title);
        if (Strings.isEmptyOrWhitespace(pushId)) {
            pushId = title;
        }
        intent.putExtra("from_top", pushId);
        super.handleIntent(intent);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pushID", pushId);
            sdkManager.sendAdjustEvenAn("pushreach", jsonObject.toString(),this);
        } catch (JSONException | NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pushID", pushId);
            sdkManager.sendAdjustEvenAn("pushshow", jsonObject.toString(),this);
        } catch (JSONException | NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public void onNewToken(@NonNull String tk) {
//        Log.i(TAG, "Refreshed token: " + tk);
    }

    private void sendNotification(String title, String messageBody) {
        Intent intent = new Intent(this, AppActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        Log.d("push", "newIntent");
        String channelId = "notify_channel_1";
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        SpannableString t = new SpannableString(title);
        t.setSpan(new StyleSpan(Typeface.BOLD), 0, t.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, channelId)
                        .setSmallIcon(R.mipmap.ic_launcher)
                        .setContentTitle(t)
                        .setContentText(messageBody)
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent);

        try {
            NotificationManager notificationManager =
                    (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel(channelId,
                        "Channel human readable title",
                        NotificationManager.IMPORTANCE_DEFAULT);
                notificationManager.createNotificationChannel(channel);
            }

            notificationManager.notify(0 /* ID of notification */, notificationBuilder.build());
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
    }
}


