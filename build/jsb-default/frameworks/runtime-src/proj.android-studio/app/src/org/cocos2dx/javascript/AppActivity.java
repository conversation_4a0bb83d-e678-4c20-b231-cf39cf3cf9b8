/****************************************************************************
 Copyright (c) 2015-2016 Chukong Technologies Inc.
 Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.

 http://www.cocos2d-x.org

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 ****************************************************************************/
package org.cocos2dx.javascript;

import org.cocos2dx.javascript.customSdk.NotificationUtil;
import org.cocos2dx.javascript.customSdk.NotifyObject;
import org.cocos2dx.javascript.customSdk.OpenAdManager;
import org.cocos2dx.javascript.customSdk.SDKUtils;
import org.cocos2dx.javascript.customSdk.SentryManager;
import org.cocos2dx.javascript.customSdk.TrackManger;
import org.cocos2dx.javascript.customSdk.sdkManager;
import org.cocos2dx.lib.Cocos2dxActivity;
import org.cocos2dx.lib.Cocos2dxGLSurfaceView;

import android.Manifest;
import android.app.Activity;
import android.app.Service;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustAttribution;
import com.adjust.sdk.AdjustConfig;
import com.adjust.sdk.OnAttributionChangedListener;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.facebook.FacebookSdk;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.common.util.Strings;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;

import java.sql.Time;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class AppActivity extends Cocos2dxActivity {
    private static Cocos2dxActivity sCocos2dxActivity;
    private static Vibrator mVibrator;
    private OpenAdManager openAdManager;
    private Activity currentActivity;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String fromTop = this.getIntent().getStringExtra("from_top");
        // DO OTHER INITIALIZATION BELOW
        sCocos2dxActivity = this;
        FacebookSdk.setGraphApiVersion("v22.0");
        sdkManager.getInstance().init(this);
        String appToken = getString(R.string.adjust_app_token);
        String environment = AdjustConfig.ENVIRONMENT_PRODUCTION;
        AdjustConfig config = new AdjustConfig(this, appToken, environment);
        config.setFbAppId(getString(R.string.facebook_app_id));
        config.setUrlStrategy(AdjustConfig.URL_STRATEGY_INDIA);
        config.setOnAttributionChangedListener(new OnAttributionChangedListener() {
            @Override
            public void onAttributionChanged(AdjustAttribution adjustAttribution) {
                Log.d("adjust", adjustAttribution.toString());
            }
        });
        Adjust.onCreate(config);

        this.getFireMessagingToken();

        mVibrator = (Vibrator) getSystemService(Service.VIBRATOR_SERVICE);
        this.askNotificationPermission();

        SentryManager.init(this);

        new Thread(
                () -> {
                    // Initialize the Google Mobile Ads SDK on a background thread.
                    MobileAds.initialize(this, initializationStatus -> {
                    });
                })
                .start();
        openAdManager = new OpenAdManager(this);

        if (!Strings.isEmptyOrWhitespace(fromTop)) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("pushID", fromTop);
                        sdkManager.sendAdjustEvenAn("push_click", jsonObject.toString(),AppActivity.getContext());
                    } catch (JSONException | NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }, 2000);

        }

        Timer timer = new Timer();
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                TrackManger.checkLog();
            }
        };
        timer.schedule(timerTask, 2000, 60 * 1000);
    }

    @Override
    public Cocos2dxGLSurfaceView onCreateView() {
        Cocos2dxGLSurfaceView glSurfaceView = new Cocos2dxGLSurfaceView(this);
        // TestCpp should create stencil buffer
        glSurfaceView.setEGLConfigChooser(5, 6, 5, 0, 16, 8);


        return glSurfaceView;
    }

    @Override
    protected void onResume() {
        super.onResume();

        Adjust.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Adjust.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Workaround in https://stackoverflow.com/questions/16283079/re-launch-of-activity-on-home-button-but-only-the-first-time/16447508
        if (!isTaskRoot()) {
            return;
        }

        try {
            sdkManager.sendAdjustEvenCC("app_end", "{}");
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        String fromTop = intent.getStringExtra("from_top");
        if (!Strings.isEmptyOrWhitespace(fromTop)) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("pushID", fromTop);
                        sdkManager.sendAdjustEvenAn("push_click", jsonObject.toString(),AppActivity.getContext());
                    } catch (JSONException | NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }, 1000);

        }
        super.onNewIntent(intent);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
    }

    public static void rateUs() {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(
                "https://play.google.com/store/apps/details?id=com.screw.nut.wood.puzzle"));
        intent.setPackage("com.android.vending");
        sCocos2dxActivity.startActivity(intent);
    }

    public static void vibrate(int duration, int amplitude) {
        if (!mVibrator.hasVibrator())
            return;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            VibrationEffect effect = VibrationEffect.createOneShot(duration, amplitude);
            mVibrator.vibrate(effect);
        } else {
            mVibrator.vibrate(duration);
        }
    }

    public static void cancelVibrate() {
        mVibrator.cancel();
    }


    // 返回键
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void getFireMessagingToken() {
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener<String>() {
            @Override
            public void onComplete(@NonNull Task<String> task) {
                if (!task.isSuccessful()) {
                    Log.i("fcm", "Fetching FCM registration token failed", task.getException());
                    return;
                }
                String token = task.getResult();
                Log.i("fcm", token);
//                String strFormat = "window.App&&window.App.event.dispatch('FCM_TOKEN', '%s');";
//                String evalString = String.format(strFormat, token);
                sdkManager.getInstance().FCSToken = token;
//                sdkManager.runEavlString(evalString);
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 8888) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d("TAG", "onRequestPermissionsResult granted");
            } else {
                Log.d("TAG", "onRequestPermissionsResult denied");
            }
        }
    }

    private void askNotificationPermission() {
        // This is only necessary for API Level > 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS) ==
                    PackageManager.PERMISSION_GRANTED) {
                // FCM SDK (and your app) can post notifications.
            } else {
                // Directly ask for the permission
                //requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS);
                requestPermissions(new String[]{Manifest.permission.POST_NOTIFICATIONS}, 8888);
            }
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    public static void addOfflineNotification(String json) {
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONArray list = new JSONArray(json);
                Map<Integer, NotifyObject> notifyObjectMap = new HashMap<>();
                for (int i = 0; i < list.length(); i++) {
                    JSONObject obj = list.getJSONObject(i);
                    NotifyObject notObj = new NotifyObject();
                    notObj.type = obj.getInt("type");
                    notObj.title = obj.getString("title");
                    notObj.content = obj.getString("content");
                    notObj.firstTime = obj.getLong("first_time");
                    notObj.pushId = obj.getString("push_id");
                    notifyObjectMap.put(i, notObj);
                }
                Log.d("Notify", "addOfflineNotification");
                NotificationUtil.notifyByAlarm(sCocos2dxActivity, notifyObjectMap);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }


    public static void clearOfflineNotification() {
        Log.d("Notify", "clearOfflineNotification");
        NotificationUtil.clearAllNotifyMsg(sCocos2dxActivity);
    }


    public static void setCustomData(String json) {
        Log.d("addLog", "setCustomData: " + json);
        TrackManger.setCustomData(sCocos2dxActivity, json);
    }
}
