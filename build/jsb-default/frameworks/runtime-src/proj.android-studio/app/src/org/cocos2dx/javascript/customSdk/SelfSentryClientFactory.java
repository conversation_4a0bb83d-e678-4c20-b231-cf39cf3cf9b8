package org.cocos2dx.javascript.customSdk;

import android.content.Context;

import io.sentry.SentryClient;
import io.sentry.android.AndroidSentryClientFactory;
import io.sentry.dsn.Dsn;

public class SelfSentryClientFactory extends AndroidSentryClientFactory {

    public static final String TAG = SelfSentryClientFactory.class.getName();
    private Context ctx;

    public SelfSentryClientFactory(Context ctx) {
        super(ctx);
        this.ctx = ctx.getApplicationContext();
        if (this.ctx == null) {
            this.ctx = ctx;
        }
    }

    @Override
    public SentryClient createSentryClient(Dsn dsn) {
        SentryClient sentryClient = super.createSentryClient(dsn);
        sentryClient.addBuilderHelper(new SelfEventBuilderHelper(ctx));
        return sentryClient;
    }

}
