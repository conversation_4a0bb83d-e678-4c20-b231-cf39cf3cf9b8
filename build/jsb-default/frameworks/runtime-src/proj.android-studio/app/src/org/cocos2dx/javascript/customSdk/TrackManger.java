package org.cocos2dx.javascript.customSdk;

import android.content.Context;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;

import org.cocos2dx.javascript.AppActivity;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import io.sentry.Sentry;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class TrackManger {
    private static final String SAVE_KEY = "push_logs";

    public static void saveLog(String action, Map<String, String> dic,Context context) {
        StringBuilder builder = new StringBuilder();
        builder.append("action=").append(action).append("`");
        for (Map.Entry<String, String> entry : dic.entrySet()) {
            builder.append("`");
            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
        }
        String data = builder.toString();
        Log.d("addLogPrint", "logData: " + data);
        addLogData(context, data);
    }

    public static void addLog(String action, Map<String, String> dic) {
        Context context = AppActivity.getContext();
        Map<String, String> commonDic = SDKUtils.getCommomParamsMap(context);
        StringBuilder builder = new StringBuilder();
        builder.append("action=").append(action);
        for (Map.Entry<String, String> entry : dic.entrySet()) {
            builder.append("`");
            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
        }
        Boolean isDev = Objects.equals(commonDic.get("pkg"), "com.cocos.demo");
        Map<String, String> mp = new HashMap<>();
        mp.put("anm", "bitrush");
        mp.put("subanm", "woodscrew");
        mp.put("did", commonDic.get("did"));
        mp.put("uid", commonDic.get("uid"));
        mp.put("debugging", "");
        String data = builder.toString();
        Log.d("addLogPrint", "logData: " + data);
        String log_data = AesUtil.aesEncrypt(data, "jx3bu9dxvv6bqi4s", "jx3bu9dxvv6bqi4s");
        mp.put("log_data", log_data);
        OkHttpClient client = new OkHttpClient();
        RequestBody body = convertToBody(mp);
        Request request = new Request.Builder().
                url(getUrl(isDev)).
                header("Content-Type", "application/x-www-form-urlencoded").
                post(body).
                build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                addLogData(context, data);
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) {
                if (response.body() != null) {
                    try {
                        String body = response.body().string();
                        JSONObject jsonObject = new JSONObject(body);
                        if (jsonObject.getInt("status") != 1) {
                            addLogData(context, data);
                        }
                    } catch (JSONException | IOException e) {
                        Sentry.capture(e);
//                        throw new RuntimeException(e);
                    }
                }
            }
        });
    }

    public static void checkLog() {
        Context context = AppActivity.getContext();
        String str = SDKUtils.readData(context, SAVE_KEY, "[]");
        try {
            JSONArray array = new JSONArray(str);
            if (array.length() <= 0) return;

            StringBuilder builder = new StringBuilder();
            long curMill = System.currentTimeMillis();
            int n = 0;
            Map<String, String> commonDic = SDKUtils.getCommomParamsMap(context);
            for (int i = 0; i < Math.min(50, array.length()); i++) {
                String dst = array.getString(i);
                String[] arr = dst.split("`");
                StringBuilder builder1 = new StringBuilder();
                boolean common = false;
                for (String s : arr) {
                    if (s.matches("logtime")){
                        common = true;
                    };
                }
                if(!common){
                    for (Map.Entry<String, String> entry : commonDic.entrySet()) {
                        builder1.append("`");
                        builder1.append(entry.getKey());
                        builder1.append("=");
                        builder1.append(entry.getValue());
                    }
                }
                dst = dst.concat(builder1.toString());

                if (n != 0) {
                        builder.append("|#|");
                }
                builder.append(dst);
                n++;

            }
            SDKUtils.saveData(context, SAVE_KEY, "[]");
            if (n == 0) {
                return;
            }
            Boolean isDev = Objects.equals(commonDic.get("pkg"), "com.cocos.demo");
            Map<String, String> mp = new HashMap<>();
            mp.put("anm", "bitrush");
            mp.put("subanm", "woodscrew");
            mp.put("did", commonDic.get("did"));
            mp.put("uid", commonDic.get("uid"));
            mp.put("debugging", "");
            String data = builder.toString();
            Log.d("addLogPrint", "data: " + data);
            String log_data = AesUtil.aesEncrypt(data, "jx3bu9dxvv6bqi4s", "jx3bu9dxvv6bqi4s");
            mp.put("log_data", log_data);
            OkHttpClient client = new OkHttpClient();
            RequestBody body = convertToBody(mp);
            Request request = new Request.Builder().
                    url(getUrl(isDev)).
                    header("Content-Type", "application/x-www-form-urlencoded").
                    post(body).
                    build();
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(@NonNull Call call, @NonNull IOException e) {
                    addLogDataS(context, array);
                }

                @Override
                public void onResponse(@NonNull Call call, @NonNull Response response) {
                    if (response.body() != null) {
                        try {
                            JSONObject jsonObject = new JSONObject(response.body().string());
                            if (jsonObject.getInt("status") != 1) {
                                addLogDataS(context, array);
                            }
                        } catch (JSONException | IOException e) {
                            Sentry.capture(e);
                        }
                    }
                }
            });
        } catch (JSONException ex) {
            Sentry.capture(ex);
        }
    }

    private static void addLogDataS(Context context, JSONArray inputArr) {
        String str = SDKUtils.readData(context, SAVE_KEY, "[]");
        try {
            JSONArray array = new JSONArray(str);
            for (int i = 0; i < array.length(); i++) {
                inputArr.put(array.get(i));
            }
            int rmN = inputArr.length() - 50;
            for (int j = 0; j < rmN; j++) {
                if (inputArr.length() > 0)
                    inputArr.remove(0);
            }
            String dataStr = inputArr.toString();
            SDKUtils.saveData(context, SAVE_KEY, dataStr);
            Log.d("addLog addLogData: ", dataStr);
        } catch (JSONException ex) {
            Sentry.capture(ex);
//            throw new RuntimeException(ex);
        }
    }

    private static void addLogData(Context context, String data) {
        String str = SDKUtils.readData(context, SAVE_KEY, "[]");
        try {
            JSONArray array = new JSONArray(str);
            array.put(data);
            if (array.length() > 50) {
                array.remove(0);
            }
            String dataStr = array.toString();
            SDKUtils.saveData(context, SAVE_KEY, dataStr);
            Log.d("addLog addLogData: ", dataStr);
        } catch (JSONException ex) {
            Sentry.capture(ex);
        }
    }

    private static String getUrl(Boolean isDev) {
        return isDev ? "http://47.74.180.115:8009/api/log/addlogs" : "https://l.brickchallenge.site/api/log/addlogs";
    }

    private static RequestBody convertToBody(Map<String, String> mp) {
        StringBuilder builder = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, String> entry : mp.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if(value == null){
                value = "";
            }
            if (i != 0) {
                builder.append("&");
            }
            builder.append(key);
            builder.append("=");
            builder.append(encode(value));
            i++;
        }
        String str = builder.toString();
//        Log.d("sendLog convertToBody: ", str);
        return RequestBody.create(str.getBytes(StandardCharsets.UTF_8));
    }

    private static String encode(String str) {
        String result = Uri.encode(str);
        return result.replace("+", "%2B");
    }

    private static int level = -1;

    public static void setCustomData(Context context, String json) {
        try {
            JSONObject obj = new JSONObject(json);
            if (obj.has("level")) {
                level = obj.getInt("level");
            }
            SDKUtils.saveData(context, "game_custom_data", json);
        } catch (JSONException ignored) {

        }
    }

    private static int getLevel() {
        if (level == -1) {
            String str = SDKUtils.readData(AppActivity.getContext(), "game_custom_data", "{}");
            try {
                JSONObject obj = new JSONObject(str);
                if (obj.has("level")) {
                    level = obj.getInt("level");
                } else {
                    level = 1;
                }
            } catch (JSONException ignored) {
                level = 1;
            }

        }
        return level;
    }

}
