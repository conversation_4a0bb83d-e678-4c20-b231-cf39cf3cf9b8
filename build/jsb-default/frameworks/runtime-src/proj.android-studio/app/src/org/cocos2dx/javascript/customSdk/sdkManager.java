package org.cocos2dx.javascript.customSdk;

import static android.content.ContentValues.TAG;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustEvent;
import com.adjust.sdk.OnDeviceIdsRead;
import com.applovin.mediation.MaxAd;
import com.applovin.mediation.MaxAdRevenueListener;
import com.applovin.mediation.MaxError;
import com.applovin.mediation.MaxReward;
import com.applovin.mediation.MaxRewardedAdListener;
import com.applovin.mediation.ads.MaxAdView;
import com.applovin.mediation.ads.MaxInterstitialAd;
import com.applovin.mediation.ads.MaxRewardedAd;
import com.applovin.sdk.AppLovinSdk;
import com.applovin.sdk.AppLovinSdkSettings;
import com.facebook.appevents.AppEventsConstants;
import com.facebook.appevents.AppEventsLogger;
import com.google.firebase.analytics.FirebaseAnalytics;

import org.cocos2dx.javascript.AppActivity;
import org.cocos2dx.javascript.R;
import org.cocos2dx.lib.Cocos2dxJavascriptJavaBridge;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class sdkManager implements MaxRewardedAdListener {
    public static AppActivity m_pkActivity;
    private static sdkManager _instance = null;
    static String gaid = "gaid";
    public static String FCSToken = "";
    private static AppLovinSdk sdk = null;
    private static Map<String, String> unitIdMap = new HashMap<String, String>();
    private static Map<String, String> unitNameMap = new HashMap<String, String>();
    private static Map<String, MaxAd> adLoadMap = new HashMap<String, MaxAd>();
    private static Map<String, String> adLoadingMap = new HashMap<String, String>();
    private static MaxRewardedAd rewardedAd;
    private static MaxRewardedAd rewardedAd1;
    private static MaxRewardedAd rewardedAd2;
    private static MaxInterstitialAd instialAd;
    private static MaxInterstitialAd instialAd1;
    private static MaxInterstitialAd instialAd2;

    private static MaxAdView bannerAd;
    private static Map<String, Integer> adRetry = new HashMap<String, Integer>();
    private static Map<String, Long> startTimeMap = new HashMap<String, Long>();
    private static int maxReloadTimes = 3;
    public String isNew = "1";
    public static String showOpen = "0";
    public static String openSwitch = "0";
    public static int adTime = 30000;
    public static Long startTime;
    static final ExecutorService cachedThreadPool = Executors.newCachedThreadPool();

    public static Double revenueUser = 0.006 ;
    public static Double revenueAvg = 0.037;
    public static Boolean isAd = false;

    public static BillingManager billingManager;

    enum AdStatus {
        LoadFailed, // 加载失败
        LoadSuccess, // 加载成功
        ShowFailed, // 显示失败
        ShowSuccess, // 显示成功
        Click, // 点击广告
        Close, // 关闭广告
        RewardUser, // 奖励用户
        RevenuePaid, // 广告收入回调
    }



    public static sdkManager getInstance() {
        if (_instance == null) {
            _instance = new sdkManager();
        }
        return _instance;
    }

    public void init(AppActivity pActivity) {
        this.unitIdMap.put("reward_1", "8a551c9a70d63ab3");
        this.unitIdMap.put("reward_2", "7c0ea17262a3bc63");
        this.unitIdMap.put("reward_3", "fa8aa339ce13d4a4");
        this.unitIdMap.put("interstitial_1", "becb411bd94bcbfc");
        this.unitIdMap.put("interstitial_2", "5ef0e977a357cecc");
        this.unitIdMap.put("interstitial_3", "2fe90f82ff0aad53");
        this.unitNameMap.put(this.unitIdMap.get("reward_1"), "reward_1");
        this.unitNameMap.put(this.unitIdMap.get("reward_2"), "reward_2");
        this.unitNameMap.put(this.unitIdMap.get("reward_3"), "reward_3");
        this.unitNameMap.put(this.unitIdMap.get("interstitial_1"), "interstitial_1");
        this.unitNameMap.put(this.unitIdMap.get("interstitial_2"), "interstitial_2");
        this.unitNameMap.put(this.unitIdMap.get("interstitial_3"), "interstitial_3");
        this.adLoadMap.put(this.unitIdMap.get("reward_1"),null);
        this.adLoadMap.put(this.unitIdMap.get("reward_2"),null);
        this.adLoadMap.put(this.unitIdMap.get("reward_3"),null);
        this.adLoadMap.put(this.unitIdMap.get("interstitial_1"),null);
        this.adLoadMap.put(this.unitIdMap.get("interstitial_2"),null);
        this.adLoadMap.put(this.unitIdMap.get("interstitial_3"),null);
        adRetry.put(this.unitIdMap.get("reward_1"), 0);
        adRetry.put(this.unitIdMap.get("reward_2"), 0);
        adRetry.put(this.unitIdMap.get("reward_3"), 0);
        adRetry.put(this.unitIdMap.get("interstitial_1"), 0);
        adRetry.put(this.unitIdMap.get("interstitial_2"), 0);
        adRetry.put(this.unitIdMap.get("interstitial_3"), 0);
        m_pkActivity = pActivity;
        String a = "38LAa0q04_ypa_dDT0zhkRyxt4cU9BFqVCiqnNTvKBJFpTChmomNUz_8IRC1oyekFreKBuGLBqCNcoLHl4VVDJ";
        AppLovinSdkSettings settings = new AppLovinSdkSettings(m_pkActivity.getApplication());
        settings.setExtraParameter("enable_black_screen_fixes", "true");
        sdk = AppLovinSdk.getInstance(a, settings, m_pkActivity.getApplication());
        sdk.setMediationProvider("MAX");
        this.createRewardedAd();
        String aid = Settings.System.getString(m_pkActivity.getContentResolver(), Settings.Secure.ANDROID_ID);
        gaid = aid;
        Adjust.getGoogleAdId(m_pkActivity, new OnDeviceIdsRead() {
            @Override
            public void onGoogleAdIdRead(String googleAdId) {
                gaid = googleAdId;
            }
        });
        billingManager = new BillingManager(m_pkActivity);
    }

    //================Ads============
    void createRewardedAd() {
        rewardedAd = MaxRewardedAd.getInstance(this.unitIdMap.get("reward_1"),sdk, m_pkActivity);
        rewardedAd.setListener(this);
        rewardedAd.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });


        rewardedAd1 = MaxRewardedAd.getInstance(this.unitIdMap.get("reward_2"),sdk, m_pkActivity);
        rewardedAd1.setListener(this);
        rewardedAd1.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });

        rewardedAd2 = MaxRewardedAd.getInstance(this.unitIdMap.get("reward_3"),sdk, m_pkActivity);
        rewardedAd2.setListener(this);
        rewardedAd2.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });


        instialAd = new MaxInterstitialAd(this.unitIdMap.get("interstitial_1"),sdk, m_pkActivity);
        instialAd.setListener(this);
        instialAd.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });
        instialAd1 = new MaxInterstitialAd(this.unitIdMap.get("interstitial_2"),sdk, m_pkActivity);
        instialAd1.setListener(this);
        instialAd1.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });
        instialAd2 = new MaxInterstitialAd(this.unitIdMap.get("interstitial_3"),sdk, m_pkActivity);
        instialAd2.setListener(this);
        instialAd2.setRevenueListener(new MaxAdRevenueListener() {
            @Override
            public void onAdRevenuePaid(@NonNull MaxAd maxAd) {
                Bundle params = new Bundle();
                params.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
                params.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.getNetworkName());
                params.putString(FirebaseAnalytics.Param.AD_PLATFORM, "applovin");
                params.putString(FirebaseAnalytics.Param.AD_UNIT_NAME,  maxAd.getAdUnitId());
                params.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                Log.d("firebase",params.toString());
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent(FirebaseAnalytics.Event.AD_IMPRESSION,params);
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("all_rev",params1);
            }
        });
        BannerAdManager.getInstance().init(m_pkActivity, sdk);


    }

    public static void sendFirebaseEvent(final String a_name, final String a_json) {
        Log.d(TAG, "sendFirebaseEvent:" + a_name + ":" + a_json);
        if (TextUtils.isEmpty(a_name)) {
            Log.e(TAG, "sendFirebaseEvent. a_name isnull ");
            return;
        }
        final Bundle params = new Bundle();
        final Bundle items = new Bundle();
        final Bundle params1 = new Bundle();
        if (!TextUtils.isEmpty(a_json)) {
            try {
                JSONObject jsonObj = new JSONObject(a_json);
                if (a_name.equals(FirebaseAnalytics.Event.PURCHASE)) {
                    String orderId = jsonObj.get("order_id").toString();
                    double value = jsonObj.getDouble("value");
                    String currency = jsonObj.get("currency").toString();
                    String item_id = jsonObj.get("item_id").toString();
                    String item_name = jsonObj.get("item_id").toString();
                    params.putDouble(FirebaseAnalytics.Param.VALUE, value);
                    params.putString(FirebaseAnalytics.Param.CURRENCY, currency);
                    params.putString(FirebaseAnalytics.Param.TRANSACTION_ID, orderId);
                    items.putString("item_id", item_id);
                    items.putString("item_name", item_name);
                    params.putBundle(FirebaseAnalytics.Param.ITEMS, items);
                    params1.putDouble(FirebaseAnalytics.Param.VALUE, value);
                    params1.putString(FirebaseAnalytics.Param.CURRENCY, currency);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        FirebaseAnalytics.getInstance(m_pkActivity).logEvent(a_name, params);
        FirebaseAnalytics.getInstance(m_pkActivity).logEvent("all_rev", params1);
    }

    // MAX Ad Listener
    @Override
    public void onAdLoaded(final MaxAd maxAd) {
        // Rewarded ad is ready to be shown. rewardedAd.isReady() will now return 'true'
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.LoadSuccess.ordinal());
        sdkManager.runEavlString(evalString);
        Log.d("maxAd",maxAd.toString());
        adLoadingMap.put(maxAd.getAdUnitId(),"0");
        this.adLoadMap.put(maxAd.getAdUnitId(), maxAd);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", this.unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            jsonObj1.put("revenue", new BigDecimal(maxAd.getRevenue()).toPlainString());
            jsonObj1.put("currency", "USD");
            jsonObj1.put("precision",maxAd.getRevenuePrecision());
            jsonObj1.put("cost",now - startTimeMap.get(maxAd.getAdUnitId()));
            sdkManager.sendAdjustEvenCC("ad_fill_suc", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        // Reset retry attempt
        adRetry.put(maxAd.getAdUnitId(),0);
    }

    @Override
    public void onAdLoadFailed(final String adUnitId, final MaxError error) {
        // Rewarded ad failed to load
        // We recommend retrying with exponentially higher delays up to a maximum delay
        // (in this case 64 seconds)
        // (in this case 64 seconds)
        this.adLoadMap.put(adUnitId,null);
        int retryAttempt = adRetry.get(adUnitId)+1;
        adRetry.put(adUnitId,retryAttempt);
        long delayMillis = TimeUnit.SECONDS.toMillis((long) Math.pow(2, Math.min(3, retryAttempt)));
//        if(retryAttempt <= maxReloadTimes) {
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    reloadAd(adUnitId, false);
//                }
//            }, delayMillis );
//        }
        Log.d("maxAd", adUnitId + error.toString());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, 0, adUnitId, AdStatus.LoadFailed.ordinal());
        sdkManager.runEavlString(evalString);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",this.unitNameMap.get(adUnitId).indexOf("reward")>-1?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", this.unitNameMap.get(adUnitId));
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", adUnitId);
            jsonObj1.put("error",error.getCode()+"");
            String msg = error.getMessage().replace('"', ' ');
            jsonObj1.put("msg", msg);
            jsonObj1.put("cost",now - startTimeMap.get(adUnitId));
            sdkManager.sendAdjustEvenCC("ad_fill_fail", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        startTimeMap.put(adUnitId, System.currentTimeMillis());
    }

    public static void reloadAd(String adUnitId, boolean retry ) {
        if (adLoadingMap.get(adUnitId) == "1") return;
        if (unitNameMap.get(adUnitId).indexOf("reward") > -1) {
            if (adLoadMap.get(adUnitId) == null) {
                adLoadingMap.put(adUnitId, "1");
                if (!retry) {
                    startTimeMap.put(adUnitId, System.currentTimeMillis());
                }
                cachedThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        MaxRewardedAd.getInstance(adUnitId, sdk, m_pkActivity).loadAd();
                    }});
                delayReloadAd(adUnitId);
                try {
                    JSONObject jsonObj1 = new JSONObject("{}");
                    jsonObj1.put("format", "Rewardedvideo");
                    jsonObj1.put("placement_id", unitNameMap.get(adUnitId));
                    jsonObj1.put("ad_platform", "applovin");
                    jsonObj1.put("unitid", adUnitId);
                    sendAdjustEvenCC("ad_pull", jsonObj1.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                
            }
        } else {
            if (adLoadMap.get(adUnitId) == null) {
                if (!retry) {
                    startTimeMap.put(adUnitId, System.currentTimeMillis());
                }
                adLoadingMap.put(adUnitId, "1");
                if (unitNameMap.get(adUnitId).indexOf("1") > -1) {
                    cachedThreadPool.execute(new Runnable() {
                        @Override
                        public void run() {
                            instialAd.loadAd();
                        }
                    });
                } else if (unitNameMap.get(adUnitId).indexOf("2") > -1) {
                    cachedThreadPool.execute(new Runnable() {
                        @Override
                        public void run() {
                            instialAd1.loadAd();
                        }
                    });
                } else {
                    cachedThreadPool.execute(new Runnable() {
                        @Override
                        public void run() {
                            instialAd2.loadAd();
                        }
                    });
                }

                delayReloadAd(adUnitId);

                try {
                    JSONObject jsonObj1 = new JSONObject("{}");
                    jsonObj1.put("format", "Interstitial");
                    jsonObj1.put("placement_id", unitNameMap.get(adUnitId));
                    jsonObj1.put("ad_platform", "applovin");
                    jsonObj1.put("unitid", adUnitId);
                    sendAdjustEvenCC("ad_pull", jsonObj1.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                
            }
        }
    }
    static Timer timer = new Timer();
    static void delayReloadAd(String adUnitId) {
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                if (adLoadingMap.get(adUnitId) != "0") {
                    adLoadingMap.put(adUnitId, "0");
                    adLoadMap.put(adUnitId, null);
                    Log.d("addLogPrint", unitNameMap.get(adUnitId)+"广告加载超时，重新加载");
                    reloadAd(adUnitId, true);
                }
            }
        };
        timer.schedule(timerTask, adTime);
    }


    @Override
    public void onAdDisplayFailed(final MaxAd maxAd, final MaxError error) {
        // Rewarded ad failed to display. We recommend loading the next ad
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement()+maxAd.getNetworkName()+error.getCode());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.ShowFailed.ordinal());
        sdkManager.runEavlString(evalString);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            jsonObj1.put("error",error.getCode()+"");
            jsonObj1.put("msg", error.getMessage() );
            sdkManager.sendAdjustEvenCC("ad_show_fail", jsonObj1.toString());
            if(maxAd.getFormat().getLabel() == "REWARDED"){
                reloadAd(unitIdMap.get("reward_1"), false);
                reloadAd(unitIdMap.get("reward_2"), false);
                reloadAd(unitIdMap.get("reward_3"), false);
            }else{
                reloadAd(unitIdMap.get("interstitial_1"), false);
                reloadAd(unitIdMap.get("interstitial_2"), false);
                reloadAd(unitIdMap.get("interstitial_3"), false);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onAdDisplayed(final MaxAd maxAd) {
        Number adFormatOrdinal = 0;
        Long now = System.currentTimeMillis();
        sdkManager.startTime = now;
        if (maxAd != null)
            adFormatOrdinal = 0;
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.ShowSuccess.ordinal());
        sdkManager.runEavlString(evalString);
        sdkManager.isAd = true;
        try {
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            jsonObj1.put("revenue", new BigDecimal(maxAd.getRevenue()).toPlainString());
            jsonObj1.put("currency", "USD");
            jsonObj1.put("precision",maxAd.getRevenuePrecision());
            jsonObj1.put("mediation", "applovin");
            jsonObj1.put("ad_network", maxAd.getNetworkName());
            jsonObj1.put("ad_format", maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");

            JSONObject jsonObj2 = new JSONObject("{}");
            jsonObj1.put("revenue", new BigDecimal(maxAd.getRevenue()).toPlainString());
            jsonObj1.put("currency", "USD");
            sdkManager.sendAdjustEvent("ad_show_suc", jsonObj1.toString());
            sdkManager.sendAdjustEvenCC("ad_show_suc", jsonObj1.toString());
            sdkManager.sendAdjustEventRevenue("ad_show_revenue",maxAd.getRevenue(),"USD",jsonObj1.toString());
            sdkManager.sendAdjustEventRevenue("all_revenue_adj",maxAd.getRevenue(),"USD",jsonObj1.toString());
            sdkManager.sendFbEvent("ad_show_suc", maxAd.getRevenue(),"USD");
            sdkManager.sendFbEvent("all_revenue_fb", maxAd.getRevenue(),"USD");
            sdkManager.sendFbEvent(AppEventsConstants.EVENT_NAME_AD_IMPRESSION, maxAd.getRevenue(),"USD");
            //获得SharedPreferences的实例
            SharedPreferences sp = m_pkActivity.getSharedPreferences("sp_name", Context.MODE_PRIVATE);
            //通过key值获取到相应的data，如果没取到，则返回后面的默认值
            String revStr = sp.getString("revenue", "0.00");
            String rev_099 = sp.getString("revenue_0.099", "0");
            String rev_361 = sp.getString("revenue_0.361", "0");
            String rev_01 = sp.getString("revenue_0.01", "0");
            String rev_006 = sp.getString("revenue_0.006", "0");
            Double rev = Double.parseDouble((revStr));
            revStr = new BigDecimal(rev +maxAd.getRevenue()).toPlainString();
            SharedPreferences.Editor editor = sp.edit();
            //以key-value形式保存数据
            editor.putString("revenue", revStr);
            //apply()是异步写入数据
            editor.apply();
            if(rev >= sdkManager.revenueAvg){
                sdkManager.sendFbEvent("user_avg_limit", maxAd.getRevenue(),"USD");
                sdkManager.sendFbEvent("fb_mobile_purchase", rev,"USD");
            }else if(rev >= sdkManager.revenueUser){
                sdkManager.sendFbEvent("user_price_limit", maxAd.getRevenue(),"USD");
            }

            if(rev >= 0.099){
                if(rev_099.equals("0")){
                    sdkManager.sendFbEvent("user_price_limit_br_single", rev,"USD");
                }
                rev_099 = "1";
                editor.putString("revenue_0.099", rev_099);
                editor.apply();
                sdkManager.sendFbEvent("user_price_limit_br_multi", rev,"USD");
            }

            if(rev >= 0.361){
                if(rev_361.equals("0")){
                    sdkManager.sendFbEvent("user_avg_limit_br_single", rev,"USD");
                }
                rev_361 = "1";
                editor.putString("revenue_0.361", rev_361);
                editor.apply();
                sdkManager.sendFbEvent("user_avg_limit_br_multi", rev,"USD");
            }

            if(rev >= 0.01){
                if(rev_01.equals("0")){
                    sdkManager.sendFbEvent("user_adprice_limit_adj_single", rev,"USD");
                }
                rev_01 = "1";
                editor.putString("revenue_0.01", rev_01);
                editor.apply();
            }

            if(rev >= 0.006){
                if(rev_006.equals("0")){
                    sdkManager.sendFbEvent("user_avg_limit_single", rev,"USD");
                }
                rev_006 = "1";
                editor.putString("revenue_0.006", rev_006);
                editor.apply();
            }

            if(rev >= 0.01){
                sendAdjustEvenCC("user_adprice_limit",jsonObj1.toString());
                sendAdjustEvent("user_adprice_limit_adj",jsonObj1.toString());
                sdkManager.sendFbEvent("user_adprice_limit_fb", maxAd.getRevenue(),"USD");
                Bundle params1 = new Bundle();
                params1.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.getRevenue());
                params1.putString(FirebaseAnalytics.Param.CURRENCY,"USD");
                FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("user_adprice_limit_gg",params1);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        adLoadMap.put(maxAd.getAdUnitId(),null);
    }

    @Override
    public void onAdClicked(final MaxAd maxAd) {
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.Click.ordinal());
        sdkManager.runEavlString(evalString);
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            sdkManager.sendAdjustEvenCC("ad_click", jsonObj1.toString());
            //sdkManager.sendFbEvent("ad_click", maxAd.getRevenue());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onAdHidden(final MaxAd maxAd) {
        // rewarded ad is hidden. Pre-load the next ad
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.Close.ordinal());
        sdkManager.runEavlString(evalString);
        adLoadMap.put(maxAd.getAdUnitId(), null);
        sdkManager.isAd = false;
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id", unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            jsonObj1.put("duration", now - sdkManager.startTime);
            sdkManager.sendAdjustEvenCC("ad_close", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        reloadAd(maxAd.getAdUnitId(), false);
    }

    @Override
    public void onRewardedVideoStarted(final MaxAd maxAd) {
    } // deprecated

    @Override
    public void onRewardedVideoCompleted(final MaxAd maxAd) {
    } // deprecated

    @Override
    public void onUserRewarded(final MaxAd maxAd, final MaxReward maxReward)
    {
        Log.d("maxAd", maxAd.getFormat().getLabel() + maxAd.getPlacement());
        String strFormat = "window.App&&window.App.event.dispatch('SDK_AD_EVENT', {adFormat:%d,placementName:'%s',status:%d});";
        String evalString = String.format(strFormat, maxAd.getFormat().getLabel() == "REWARDED" ? 0 : 1 , maxAd.getPlacement(), AdStatus.RewardUser.ordinal());
        sdkManager.runEavlString(evalString);
        // Rewarded ad was displayed and user should receive the reward
        try {
            Long now = System.currentTimeMillis();
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format",maxAd.getFormat().getLabel() == "REWARDED" ?"Rewardedvideo":"Interstitial");
            jsonObj1.put("placement_id",unitNameMap.get(maxAd.getAdUnitId()));
            jsonObj1.put("placement_name",maxAd.getPlacement());
            jsonObj1.put("ad_platform", "applovin");
            jsonObj1.put("unitid", maxAd.getAdUnitId());
            jsonObj1.put("network", maxAd.getNetworkName());
            jsonObj1.put("network_id", maxAd.getNetworkPlacement());
            sdkManager.sendAdjustEvenCC("ad_reward_suc", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    public static void copyStr(final String str) {
        ClipboardManager clipboardManager =(ClipboardManager) m_pkActivity.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clipData = ClipData.newPlainText("label", str);
        clipboardManager.setPrimaryClip(clipData);
    }

    public static boolean playRewardAd(final String placementName) {
        MaxAd reward1 = adLoadMap.get(unitIdMap.get("reward_1"));
        MaxAd reward2 = adLoadMap.get(unitIdMap.get("reward_2"));
        MaxAd reward3 = adLoadMap.get(unitIdMap.get("reward_3"));

        Double value1 = -1.00;
        Double value2 = -1.00;
        Double value3 = -1.00;

        if(reward1 != null && rewardedAd.isReady()){
            value1 = reward1.getRevenue();
        }
        if(reward2 != null&& rewardedAd1.isReady()){
            value2 = reward2.getRevenue();
        }
        if(reward3 != null&& rewardedAd2.isReady()){
            value3 = reward3.getRevenue();
        }
        Log.d("maxAd", value1 + "----" + value2 + "----" + value3);
        if(value1 >= value2){
            if(value1 >= value3){
                rewardedAd.showAd(placementName,"reward_1");
            }else{
                rewardedAd2.showAd(placementName,"reward_3");
            }
        }else{
            if(value2 >= value3){
                rewardedAd1.showAd(placementName,"reward_2");
            }else{
                rewardedAd2.showAd(placementName,"reward_3");
            }
        }
        return true;
    }


    public static boolean showBanner(final String placementName) {
        BannerAdManager.getInstance().showBanner(placementName);
        return true;
    }

    public static boolean hideBanner(final String placementName) {
        BannerAdManager.getInstance().hideBanner(placementName);
        return true;
    }

    public static boolean playRewardAdIsReady(final String placementName) {
        Log.d("Ad=>", "playRewardAdIsReady");
        MaxAd reward1 = adLoadMap.get(unitIdMap.get("reward_1"));
        MaxAd reward2 = adLoadMap.get(unitIdMap.get("reward_2"));
        MaxAd reward3 = adLoadMap.get(unitIdMap.get("reward_3"));
        boolean ready = false;
        if(reward1 != null){
            ready = MaxRewardedAd.getInstance(unitIdMap.get("reward_1"),sdk,m_pkActivity).isReady();
        }
        if(reward2 != null && ready == false){
            ready = MaxRewardedAd.getInstance(unitIdMap.get("reward_2"),sdk,m_pkActivity).isReady();
        }
        if(reward3 != null && ready == false){
            ready = MaxRewardedAd.getInstance(unitIdMap.get("reward_3"),sdk,m_pkActivity).isReady();
        }
        return ready;
    }

    public static boolean playInterstitialAd(final String placementName) {
        Log.d("Ad=>", "playInterstitialAd");
        MaxAd reward1 = adLoadMap.get(unitIdMap.get("interstitial_1"));
        MaxAd reward2 = adLoadMap.get(unitIdMap.get("interstitial_2"));
        MaxAd reward3 = adLoadMap.get(unitIdMap.get("interstitial_3"));
        MaxInterstitialAd inter = null;
        Double value1 = -1.00;
        Double value2 = -1.00;
        Double value3 = -1.00;

        if(reward1 != null && instialAd.isReady()){
            value1 = reward1.getRevenue();
        }
        if(reward2 != null && instialAd1.isReady()){
            value2 = reward2.getRevenue();
        }
        if(reward3 != null && instialAd2.isReady()){
            value3 = reward3.getRevenue();
        }
        Log.d("maxAd", value1 + "----" + value2+ "----" + value3);
        if(value1 >= value2){
            if(value1 >= value3){
                instialAd.showAd(placementName,"interstitial_1");
            }else{
                instialAd2.showAd(placementName,"interstitial_3");
            }
        }else{
            if(value2 >= value3){
                instialAd1.showAd(placementName,"interstitial_2");
            }else{
                instialAd2.showAd(placementName,"interstitial_3");
            }
        }
        return true;
    }

    public static boolean playInterstitialAdIsReady(final String placementName) {
        Log.d("Ad=>", "playInterstitialAdIsReady");
        MaxAd reward1 = adLoadMap.get(unitIdMap.get("interstitial_1"));
        MaxAd reward2 = adLoadMap.get(unitIdMap.get("interstitial_2"));
        MaxAd reward3 = adLoadMap.get(unitIdMap.get("interstitial_3"));
        boolean ready =false;
        if(reward1 != null){
            ready = instialAd.isReady();
        }
        if(reward2 != null && !ready){
            ready = instialAd1.isReady();
        }
        if(reward3 != null && !ready){
            ready = instialAd2.isReady();
        }
        return ready;
    }



    //================Utils============
    public static PackageInfo getPackageInfo() {
        try {
            PackageManager manager = m_pkActivity.getPackageManager();
            PackageInfo info = manager.getPackageInfo(m_pkActivity.getPackageName(), 0);
            return info;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void runEavlString(final String eavlString) {
        m_pkActivity.runOnGLThread(new Runnable() {
            @Override
            public void run() {
                Cocos2dxJavascriptJavaBridge.evalString(eavlString);
            }
        });
    }


    //================JSB============
    public static void JSBNotify(final String a_name, final String a_json) {
        Log.d(TAG, "notifySDK:" + a_name + ":" + a_json);
        if (TextUtils.isEmpty(a_name)) {
            Log.e(TAG, "notifySDK. a_name isnull ");
            return;
        }
        switch (a_name) {
            case "vibrator": {
            }
            break;
            case "initSdk": {
                Long start = System.currentTimeMillis();
                sdk.initializeSdk(appLovinSdkConfiguration -> {
                    try {
                        JSONObject jsonObj1 = new JSONObject("{}");
                        sdkManager.sendAdjustEvenCC("ad_app_load", jsonObj1.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }

                    try {
                        Long now = System.currentTimeMillis();
                        JSONObject jsonObj = new JSONObject("{}");
                        jsonObj.put("game_version", SDKUtils.getVersionName(m_pkActivity));
                        jsonObj.put("duration", now-start);
                        sdkManager.sendAdjustEvenCC("initialization_sdk", jsonObj.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                    sdkManager.loadAds();
                    BannerAdManager.createBanner();
                });
            }
            break;
            case "initRevenue": {
                String[] split = a_json.split("\\|");
                sdkManager.revenueUser = Double.parseDouble(split[0]);
                sdkManager.revenueAvg = Double.parseDouble(split[1]);
            }
            break;
            case "updateLevel": {
                sdkManager.showOpen = a_json;
            }
            break;
            case "updateOpenSwitch": {
                sdkManager.openSwitch = a_json;
            }
            break;
            case "passLevel": {
                Log.d("level", a_json);
                try {
                    String level = a_json;
                    final Bundle params1 = new Bundle();
                    //获得SharedPreferences的实例
                    SharedPreferences sp = m_pkActivity.getSharedPreferences("sp_name", Context.MODE_PRIVATE);
                    //通过key值获取到相应的data，如果没取到，则返回后面的默认值
                    String revStr = sp.getString("revenue", "0.00");
                    Double rev = Double.parseDouble((revStr));
                    if(level.equals("4")){
                        Log.d("level", "firebase_3");
                        sdkManager.sendFbEvent("level_3_pass_fb",Double.valueOf("0.00"),"USD");
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_3_pass_gg",params1);
                    }else if(level.equals("5")){
                        Log.d("level", "firebase_4");
                        sdkManager.sendFbEvent("level_4_pass_fb",Double.valueOf("0.00"),"USD");
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_4_pass_gg",params1);
                        if(rev >= 0.006){
                            sdkManager.sendAdjustEvent("level_4_pass_adj_single","{}");
                        }
                    }else if(level.equals("7")){
                        sdkManager.sendFbEvent("level_6_pass_fb",Double.valueOf("0.00"),"USD");
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_6_pass_gg",params1);
                    }else if(level.equals("8")){
                        sdkManager.sendFbEvent("level_7_pass_fb",Double.valueOf("0.00"),"USD");
                    }else if(level.equals("11")){
                        sdkManager.sendFbEvent("level_10_pass_fb",Double.valueOf("0.00"),"USD");
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_10_pass_gg",params1);
                    }else if(level.equals("14")){
                        sdkManager.sendFbEvent("level_13_pass_fb",Double.valueOf("0.00"),"USD");
                    }else if(level.equals("16")){
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_15_pass_gg",params1);
                        sdkManager.sendAdjustEvent("level_15_pass_adj","{}");
                    }
                    else if(level.equals("19")){
                        sdkManager.sendFbEvent("level_18_pass_fb",Double.valueOf("0.00"),"USD");
                    }else if(level.equals("21")){
                        FirebaseAnalytics.getInstance((m_pkActivity)).logEvent("level_20_pass_gg",params1);
                    }
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
            break;
            default:
                break;
        }
    }

    public static String JSBGetDeviceInfo() {
        try {
            JSONObject respObj = new JSONObject();
            // //版本名
            // String app_version = getVersionName(m_pkActivity);
            // Log.i("app_version ",app_version);
            // respObj.put("app_version",app_version);
            // 时区
            int timezone = SDKUtils.getTimeZone();
            Log.i("timezone ", String.valueOf(timezone));
            //// 发行SDK参数//////////////////////////////////////
            String pkg = m_pkActivity.getPackageName();
            Log.i("pkg ", pkg);
            String aid = Settings.System.getString(m_pkActivity.getContentResolver(), Settings.Secure.ANDROID_ID);
            Log.i("aid ", aid);
            String country = SDKUtils.getCountry(m_pkActivity);
            Log.i("country ", country);
            String language = Locale.getDefault().getLanguage();
            Log.i("language ", language);

            String appver = Integer.toString(getPackageInfo().versionCode);
            Log.i("appver ", appver);
            String appver_name = getPackageInfo().versionName;
            Log.i("appver_name ", appver_name);
            String fcmToken = sdkManager.FCSToken;
            Log.i("fcmToken ", fcmToken);
            String buildIndex = "35";
            Log.i("buildIndex ", buildIndex);

            respObj.put("timezone", timezone);
            respObj.put("did", aid);
            respObj.put("pkg", pkg);
            respObj.put("gaid", aid);
            respObj.put("aid", aid);
            respObj.put("country", country);
            respObj.put("language", language);
            respObj.put("appver", appver);
            respObj.put("appver_name", appver_name);
            respObj.put("fcmToken", fcmToken);
            respObj.put("buildIndex", buildIndex);
            respObj.put("total_memory", String.valueOf(SDKUtils.getTotalMemorySize(m_pkActivity)));
            respObj.put("bucket", String.valueOf(SDKUtils.getBucket(m_pkActivity)));
            return respObj.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getCommonParams() {
        final Map<String, String> comParams = SDKUtils.getCommomParamsMap(m_pkActivity);
        JSONObject jsonObject = new JSONObject(comParams);
        return jsonObject.toString();
    }

    public static String getFcmToken() {
        return FCSToken;
    }

    public static void sendAdjustEvenCC(final String a_name, final String a_json)
            throws NoSuchFieldException, IllegalAccessException {
        String strFormat = "window.App&&window.App.event.dispatch('ADD_LOGS', {name:'%s',data:'%s'});";
        String evalString = String.format(strFormat,a_name ,a_json);
        sdkManager.runEavlString(evalString);
//        try {
//            Map<String, String> mp = new HashMap<>();
//            JSONObject jsonObj = new JSONObject(a_json);
//            for (Iterator<String> it = jsonObj.keys(); it.hasNext(); ) {
//                String str = it.next();
//                mp.put(str, jsonObj.get(str).toString());
//            }
//            TrackManger.addLog(a_name,mp);
//        } catch (JSONException ignored) {
//
//        }

    }

    public static void sendAdjustEvenAn(final String a_name, final String a_json, final Context context)
            throws NoSuchFieldException, IllegalAccessException {
//        String strFormat = "window.App&&window.App.event.dispatch('ADD_LOGS', {name:'%s',data:'%s'});";
//        String evalString = String.format(strFormat,a_name ,a_json);
//        sdkManager.runEavlString(evalString);
        try {
            Map<String, String> mp = new HashMap<>();
            JSONObject jsonObj = new JSONObject(a_json);
            for (Iterator<String> it = jsonObj.keys(); it.hasNext(); ) {
                String str = it.next();
                mp.put(str, jsonObj.get(str).toString());
            }
            TrackManger.saveLog(a_name,mp,context);
        } catch (JSONException ignored) {

        }

    }

    public static void sendAdjustEvent(final String a_name, final String a_json)
            throws NoSuchFieldException, IllegalAccessException {
        Log.d(TAG, "sendAdjustEvent:" + a_name + ":" + a_json);
        if (TextUtils.isEmpty(a_name)) {
            Log.e(TAG, "sendAdjustEvent. a_name isnull ");
            return;
        }
        Field field = R.string.class.getField(a_name);

        int rsId = (Integer) field.get(a_name);
        String eventCode = m_pkActivity.getString(rsId);
        AdjustEvent adjustEvent = new AdjustEvent(eventCode);

        final Map<String, String> comParams = SDKUtils.getCommomParamsMap(m_pkActivity);
        for (Map.Entry<String, String> entry : comParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key == "did")
                value = gaid;
            adjustEvent.addCallbackParameter(key, value);
        }

        if (!TextUtils.isEmpty(a_json)) {
            try {
                JSONObject jsonObj = new JSONObject(a_json);
                for (Iterator<String> it = jsonObj.keys(); it.hasNext(); ) {
                    String str = it.next();
                    System.out.println(str + ":" + jsonObj.get(str));
                    adjustEvent.addCallbackParameter(str, jsonObj.get(str).toString());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        Adjust.trackEvent(adjustEvent);
    }

    public static void sendFbEvent(final String a_name, final double value, final String currency)
            throws NoSuchFieldException, IllegalAccessException {
        Log.d(TAG, "sendFbEvent:" + a_name + ":" + value + ":" + currency);
        if (TextUtils.isEmpty(a_name)) {
            Log.e(TAG, "sendFbEvent. a_name isnull ");
            return;
        }
        AppEventsLogger logger = AppEventsLogger.newLogger(m_pkActivity);

        Bundle params = new Bundle();
        params.putString(AppEventsConstants.EVENT_PARAM_CURRENCY, currency);

        logger.logEvent(a_name,
                value,
                params);
    }

    public static void sendAdjustEventRevenue(final String a_name, double revenue, String currency, final  String a_json)
            throws NoSuchFieldException, IllegalAccessException {
        Log.d(TAG, "sendAdjustEventRevenue:" + a_name + ":" + revenue);
        if (TextUtils.isEmpty(a_name)) {
            Log.e(TAG, "sendAdjustEvent. a_name isnull ");
            return;
        }
        Field field = R.string.class.getField(a_name);

        int rsId = (Integer) field.get(a_name);
        String eventCode = m_pkActivity.getString(rsId);
        AdjustEvent adjustEvent = new AdjustEvent(eventCode);

        final Map<String, String> comParams = SDKUtils.getCommomParamsMap(m_pkActivity);
        for (Map.Entry<String, String> entry : comParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key == "did")
                value = gaid;
            adjustEvent.addCallbackParameter(key, value);
        }
        if (!TextUtils.isEmpty(a_json)) {
            try {
                JSONObject jsonObj = new JSONObject(a_json);
                for (Iterator<String> it = jsonObj.keys(); it.hasNext(); ) {
                    String str = it.next();
                    System.out.println(str + ":" + jsonObj.get(str));
                    adjustEvent.addCallbackParameter(str, jsonObj.get(str).toString());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        adjustEvent.setRevenue(revenue, currency);
        Adjust.trackEvent(adjustEvent);
    }

    public static String ForceLoadAds() {
        loadAds();
        return "";
    }

    public static void updateAdTime(final String time) {
        int adTime = Integer.parseInt(time) *1000;
        Log.d("adtime", time);
    }

    public static void loadAds() {
        Log.d("maxAd","loadAds");
        reloadAd(unitIdMap.get("reward_1"), false);
        reloadAd(unitIdMap.get("reward_2"), false);
        reloadAd(unitIdMap.get("reward_3"), false);
        reloadAd(unitIdMap.get("interstitial_1"), false);
        reloadAd(unitIdMap.get("interstitial_2"), false);
        reloadAd(unitIdMap.get("interstitial_3"), false);
    }
    public static boolean JSBPlayRewardAd(final String placementName,String isNew) {
        Log.i("Ad=>", "playRewardAd");
        sdkManager.getInstance().isNew = isNew;
        try {
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Rewardedvideo");
            jsonObj1.put("placement_name",placementName);
            sdkManager.sendAdjustEvenCC("ad_show_start", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        boolean ready = sdkManager.playRewardAdIsReady(placementName);
        if(ready){
            sdkManager.playRewardAd( placementName);
        }
        return ready;
    }

    public static boolean JSBPlayInterstitialAd(final String placementName) {
        Log.i("Ad=>", "playInterstitialAd");
        try {
            JSONObject jsonObj1 = new JSONObject("{}");
            jsonObj1.put("format","Interstitial");
            jsonObj1.put("placement_name",placementName);
            sdkManager.sendAdjustEvenCC("ad_show_start", jsonObj1.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        boolean ready = sdkManager.playInterstitialAdIsReady(placementName);
        if(ready){
            sdkManager.playInterstitialAd( placementName );
        }
        return  ready;
    }


    public static boolean JSBPlayRewardAdIsReady(final String placementName) {
        boolean ready = sdkManager.playRewardAdIsReady(placementName);
        Log.i("Ad=>", "JSBPlayRewardAdIsReady " + ready);
        return ready;
    }

    public static boolean JSBInterstitialAdIsReady(final String placementName) {
        boolean ready = playInterstitialAdIsReady(placementName);
        Log.i("Ad=>", "JSBInterstitialAdIsReady " + ready);
        return ready;
    }
    
}
