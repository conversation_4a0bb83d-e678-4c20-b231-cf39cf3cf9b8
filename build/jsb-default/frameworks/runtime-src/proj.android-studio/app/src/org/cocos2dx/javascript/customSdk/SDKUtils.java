package org.cocos2dx.javascript.customSdk;

import static android.content.Context.MODE_PRIVATE;
import static org.cocos2dx.javascript.customSdk.sdkManager.gaid;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.provider.Settings;
import android.telephony.TelephonyManager;

import androidx.core.os.ConfigurationCompat;
import androidx.core.os.LocaleListCompat;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustAttribution;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;

public class SDKUtils {
    private static final String OTHERS_COUNTRY = "unknown";
    private static final int COUNTRY_FROM_NONE = 0;
    private static final int COUNTRY_FROM_SIM = 1;
    private static final int COUNTRY_FROM_NETWORK = 2;
    private static final int COUNTRY_FROM_LANGUAGE = 3;
    private static final String COUNTRY_US = "US";
    private static final String COUNTRY_GB = "GB";
    private static String mCountryIsoSystem;
    private static int mCountryFrom = COUNTRY_FROM_NONE;

    public static String getCountry(Context context) {
        if (mCountryIsoSystem != null && !mCountryIsoSystem.isEmpty())
            return mCountryIsoSystem;

        mCountryIsoSystem = getCountryIso(context);
        if (mCountryFrom == COUNTRY_FROM_LANGUAGE
                && (mCountryIsoSystem.equals(COUNTRY_US) || mCountryIsoSystem.equals(COUNTRY_GB))) {
            mCountryIsoSystem = OTHERS_COUNTRY;
        }
        return mCountryIsoSystem;
    }

    private static  String getCountryIso(Context context) {
        mCountryFrom = COUNTRY_FROM_NONE;

        try {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

            // Get country ISO from SIM card
            String iso = tm.getSimCountryIso();
            if (iso != null && !iso.isEmpty()) {
                mCountryFrom = COUNTRY_FROM_SIM;
                return iso.toUpperCase(Locale.US);
            }

            // If not available, get country ISO from network operator
            iso = tm.getNetworkCountryIso();
            if (iso != null && !iso.isEmpty()) {
                mCountryFrom = COUNTRY_FROM_NETWORK;
                return iso.toUpperCase(Locale.US);
            }

            // If still not available, get country ISO from language settings
            iso = Locale.getDefault().getCountry();
            if (iso.isEmpty()) {
                LocaleListCompat localeListCompat = ConfigurationCompat
                        .getLocales(Resources.getSystem().getConfiguration());
                if (localeListCompat.size() > 0 && localeListCompat.get(0) != null) {
                    iso = localeListCompat.get(0).getCountry();
                }
            }
            if (!iso.isEmpty()) {
                mCountryFrom = COUNTRY_FROM_LANGUAGE;
                return iso.toUpperCase(Locale.US);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // If still not available, return empty string
        return "";
    }

    public static int getTimeZone() {
        TimeZone timeZone = TimeZone.getDefault();
        String id = timeZone.getID(); // 获取时区id，如“Asia/Shanghai”
        String name = timeZone.getDisplayName(); // 获取名字，如“”
        String shotName = timeZone.getDisplayName(false, TimeZone.SHORT); // 获取名字，如“GMT+08:00”
        int time = timeZone.getRawOffset(); // 获取时差，返回值毫秒
        int zone = time / 3600 / 1000;
        return zone;
    }

    private static String getPackageName(Context context) {
        return context.getPackageName();
    }

    private static String getVersionCode(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(getPackageName(context), 0);
            if (packageInfo != null) {
                return String.valueOf(packageInfo.versionCode);
            }
        } catch (PackageManager.NameNotFoundException exception) {
            return "";
        }
        return "";
    }

    public static String getVersionName(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(getPackageName(context), 0);
            if (packageInfo != null) {
                return packageInfo.versionName;
            }
        } catch (PackageManager.NameNotFoundException exception) {
            return "";
        }
        return "";
    }

    public static Map<String, String> getCommomParamsMap(Context context) {
        AdjustAttribution attribution = Adjust.getAttribution();
        Map<String, String> commomParamMap = new HashMap<>();
        if (attribution == null) {
            commomParamMap.put("referrer", "");
            commomParamMap.put("sub", "");
            commomParamMap.put("cha", "");
        } else {

            try {
                JSONObject jsonObj1 = new JSONObject("{}");
                jsonObj1.put("adid",attribution.adid);
                jsonObj1.put("tracker_token", attribution.trackerToken);
                jsonObj1.put("tracker_name",attribution.trackerName);
                jsonObj1.put("network", attribution.network);
                jsonObj1.put("campaign",attribution.campaign);
                jsonObj1.put("adgroup", attribution.adgroup);
                commomParamMap.put("referrer", jsonObj1.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            commomParamMap.put("sub", attribution.campaign);
            commomParamMap.put("cha", attribution.network);
        }
        commomParamMap.put("debugging", "");
        commomParamMap.put("dev_first_install_time", "");
        commomParamMap.put("project", "");
        commomParamMap.put("ctag", "");
        commomParamMap.put("dev_channel", "");
        commomParamMap.put("rel", "");
        commomParamMap.put("abslot", String.valueOf(getBucket(context)));
        commomParamMap.put("ram", "");
        commomParamMap.put("cpuf", "");
        commomParamMap.put("cpu", "");
        commomParamMap.put("sh", "");
        commomParamMap.put("sw", "");
        commomParamMap.put("city", "");
        commomParamMap.put("ste", "");
        commomParamMap.put("lat", "");
        commomParamMap.put("lng", "");
        commomParamMap.put("sty", "");
        commomParamMap.put("alan", "");
        commomParamMap.put("lan", "");
        commomParamMap.put("reg", "");

        commomParamMap.put("idfa", "1");
        commomParamMap.put("idfv", "0");
        commomParamMap.put("gaid", gaid);

        commomParamMap.put("uid", getAndroidId(context));
        commomParamMap.put("anm", "bitrush");
        commomParamMap.put("subanm", "woodscrew");
        commomParamMap.put("pkg", getPackageName(context));
        commomParamMap.put("verc", getVersionCode(context));
        commomParamMap.put("ver", getVersionName(context));
        commomParamMap.put("pf", "android");
        commomParamMap.put("did", gaid); // 取gaid即可，这里不实现具体逻辑
        commomParamMap.put("aid", getAndroidId(context));
        commomParamMap.put("brd", Build.BRAND);
        commomParamMap.put("mod", Build.MODEL);
        commomParamMap.put("cou", getCountry(context));
        commomParamMap.put("slan", String.valueOf(Locale.getDefault()));
        commomParamMap.put("has_sim_card", hasSimCard(context));
        commomParamMap.put("sim_mcc_mnc", getSimOperator(context));
        commomParamMap.put("net_mcc_mnc", getNetOperator(context));
        commomParamMap.put("isp", getNetOperator(context));
        commomParamMap.put("net", getNetWorkType(context));
        commomParamMap.put("sys_vc", String.valueOf(Build.VERSION.SDK_INT));
        commomParamMap.put("sys_vn", Build.VERSION.RELEASE);
        commomParamMap.put("logtime", String.valueOf(System.currentTimeMillis()));
        commomParamMap.put("log_id", String.valueOf(UUID.randomUUID()));
        commomParamMap.put("sid", getSoftwareId(context));
        commomParamMap.put("bucket", String.valueOf(getBucket(context)));
        commomParamMap.put("os", String.valueOf(Build.VERSION.SDK_INT));
        return commomParamMap;
    }

    @SuppressLint("HardwareIds")
    public static String getAndroidId(Context context) {
        return Settings.Secure.getString(context.getContentResolver(), "android_id");
    }

    private static String hasSimCard(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        int simState = telephonyManager.getSimState();
        if (simState == TelephonyManager.SIM_STATE_ABSENT || simState == TelephonyManager.SIM_STATE_NETWORK_LOCKED) {
            return "0";
        } else {
            return "1";
        }
    }

    private static String getSimOperator(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        return telephonyManager.getSimOperator();
    }

    private static String getNetOperator(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        return telephonyManager.getNetworkOperator();
    }

    private static String getNetWorkType(Context context) {
        if (context == null) {
            return "unknown";
        }
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return "unknown";
        }
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        if (activeNetworkInfo != null && activeNetworkInfo.isConnected()) {
            if (activeNetworkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                return "wifi"; // WIFI
            } else if (activeNetworkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                switch (activeNetworkInfo.getSubtype()) {
                    case TelephonyManager.NETWORK_TYPE_GSM:
                    case TelephonyManager.NETWORK_TYPE_GPRS:
                    case TelephonyManager.NETWORK_TYPE_CDMA:
                    case TelephonyManager.NETWORK_TYPE_EDGE:
                    case TelephonyManager.NETWORK_TYPE_1xRTT:
                    case TelephonyManager.NETWORK_TYPE_IDEN:
                        return "2G"; // 2G
                    case TelephonyManager.NETWORK_TYPE_TD_SCDMA:
                    case TelephonyManager.NETWORK_TYPE_EVDO_A:
                    case TelephonyManager.NETWORK_TYPE_UMTS:
                    case TelephonyManager.NETWORK_TYPE_EVDO_0:
                    case TelephonyManager.NETWORK_TYPE_HSDPA:
                    case TelephonyManager.NETWORK_TYPE_HSUPA:
                    case TelephonyManager.NETWORK_TYPE_HSPA:
                    case TelephonyManager.NETWORK_TYPE_EVDO_B:
                    case TelephonyManager.NETWORK_TYPE_EHRPD:
                    case TelephonyManager.NETWORK_TYPE_HSPAP:
                        return "3G"; // 3G
                    case TelephonyManager.NETWORK_TYPE_IWLAN:
                    case TelephonyManager.NETWORK_TYPE_LTE:
                        return "4G"; // 4G
                    default:
                        return "unknown";
                }
            }
        }
        return "no_net";
    }

    private static int bucket = -1;
    private static String sid = "";

    public static int getBucket(Context context) {
        if (bucket > 0)
            return bucket;
        bucket = readData(context, "bucket", -1);
        if (bucket > 0)
            return bucket;
        bucket = Math.abs(getSoftwareId(context).hashCode() % 100);
        saveData(context, "bucket", bucket);
        return bucket;
    }


    public static long getTotalMemorySize(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        return memoryInfo.totalMem/1024/1024;
    }

    private static String getSoftwareId(Context context) {
        if (!sid.isEmpty())
            return sid;
        sid = readData(context, "sid", "");
        if (!sid.isEmpty())
            return sid;
        sid = String.valueOf(UUID.randomUUID());
        saveData(context, "sid", sid);
        return sid;
    }

    public static void saveData(Context context, String key, int value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("my_preferences", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(key, value);
        editor.apply();
    }

    public static void saveData(Context context, String key, String value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("my_preferences", MODE_PRIVATE);
        if(sharedPreferences == null){
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(key, value);
        editor.apply();
    }

    public static int readData(Context context, String key, int defaultValue) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("my_preferences", MODE_PRIVATE);
        return sharedPreferences.getInt(key, defaultValue);
    }

    public static String readData(Context context, String key, String defaultValue) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("my_preferences", MODE_PRIVATE);
        return sharedPreferences.getString(key, defaultValue);
    }
}
