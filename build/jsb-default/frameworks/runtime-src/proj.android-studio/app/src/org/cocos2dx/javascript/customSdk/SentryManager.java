package org.cocos2dx.javascript.customSdk;


import android.content.Context;

import io.sentry.Sentry;
import io.sentry.event.EventBuilder;
import io.sentry.event.User;

public class SentryManager {
    public static void init(Context context) {

        Sentry.init("https://<EMAIL>/21", new SelfSentryClientFactory(context));

        User user = new User(SDKUtils.getAndroidId(context), "", "", "");

        Sentry.getContext().setUser(user);
    }


    private static String lastStack = "";

    public static void postException(int category, String name, String reason, String stack) {
        if (lastStack.equals(stack)) {
            return;
        }
        lastStack = stack;
        EventBuilder eventBuilder = new EventBuilder();
        eventBuilder.withTag(name, reason);
        eventBuilder.withMessage(stack);
        Sentry.capture(eventBuilder);
    }
}
