package org.cocos2dx.javascript.customSdk;


import android.util.Base64;

import java.nio.charset.StandardCharsets;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AesUtil {
    /**
     * 适配Node.js中AES-CBC-ZeroPadding加密
     *
     * @param sSrc           -- 待加密内容
     * @param sKey           -- 加密密钥
     * @param ivParameter    -- 偏移量
     * @return Base64编码后的字符串，"":编码出错
     */
    public static String aesEncrypt(String sSrc, String sKey, String ivParameter) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            cipher.init(Cipher.ENCRYPT_MODE,
                    new SecretKeySpec(sKey.getBytes(StandardCharsets.UTF_8), "AES"),
                    new IvParameterSpec(ivParameter.getBytes(StandardCharsets.UTF_8)));
            int blockSize = cipher.getBlockSize();
            //用'\0'填充长度至整数倍,对应node.js中ZeroPadding
            StringBuilder dataBuilder = new StringBuilder(sSrc);
            if (sSrc.length() % blockSize != 0) {
                for (int i = 0; i<blockSize-(sSrc.length() % blockSize); i++){
                    dataBuilder.append("\0");
                }
            }
            byte[] encrypted = cipher.doFinal(dataBuilder.toString().getBytes(StandardCharsets.UTF_8));
            return Base64.encodeToString(encrypted,0).trim();
        }catch (Exception e){
            return "";
        }
    }
}
