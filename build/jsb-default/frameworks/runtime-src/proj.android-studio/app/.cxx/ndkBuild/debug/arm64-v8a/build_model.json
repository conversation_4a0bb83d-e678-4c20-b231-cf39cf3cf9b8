{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "originalCxxBuildFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.cxx/ndkBuild/debug/arm64-v8a", "cxxBuildFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.cxx/ndkBuild/debug/arm64-v8a", "abiPlatformVersion": 21, "variant": {"buildSystemArgumentList": ["NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "objFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/build/intermediates/ndkBuild/debug/obj/local", "soFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/build/intermediates/ndkBuild/debug/lib", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A"], "buildTargetSet": ["cocos2djs"], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.cxx", "splitsAbiFilterSet": [], "intermediatesFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/build/intermediates", "gradleModulePathName": ":game", "moduleRootFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app", "moduleBuildFile": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/build.gradle", "makeFile": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/jni/Android.mk", "buildSystem": "NDK_BUILD", "ndkFolder": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "SYSTEM", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "originalCmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "cmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "ARM64_V8A": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "X86": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "X86_64": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio", "cxxFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/.cxx", "compilerSettingsCacheFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/.cxx", "sdkFolder": "/Users/<USER>/Library/Android/sdk", "isNativeCompilerSettingsCacheEnabled": false, "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false, "isV2NativeModelEnabled": true}, "nativeBuildOutputLevel": "QUIET"}, "prefabDirectory": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.cxx/ndkBuild/debug/prefab"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/app/.cxx/ndkBuild/debug/prefab/arm64-v8a"}