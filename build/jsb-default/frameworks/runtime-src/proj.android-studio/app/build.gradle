import org.apache.tools.ant.taskdefs.condition.Os

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

android {
    compileSdkVersion PROP_COMPILE_SDK_VERSION.toInteger()
    buildToolsVersion PROP_BUILD_TOOLS_VERSION
    namespace "org.cocos2dx.javascript"

    defaultConfig {
        applicationId "com.cocos.demo"
        minSdkVersion PROP_MIN_SDK_VERSION
        targetSdkVersion PROP_TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0.0"

        externalNativeBuild {
            ndkBuild {
                if (!project.hasProperty("PROP_NDK_MODE") || PROP_NDK_MODE.compareTo('none') != 0) {
                    // skip the NDK Build step if PROP_NDK_MODE is none
                    targets 'cocos2djs'
                    arguments 'NDK_TOOLCHAIN_VERSION=clang'

                    def module_paths = [project.file("../../../cocos2d-x"),
                                        project.file("../../../cocos2d-x/cocos"),
                                        project.file("../../../cocos2d-x/external")]
                    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
                        arguments 'NDK_MODULE_PATH=' + module_paths.join(";")
                    }
                    else {
                        arguments 'NDK_MODULE_PATH=' + module_paths.join(':')
                    }

                    arguments '-j' + Runtime.runtime.availableProcessors()
                }
            }
            ndk {
                abiFilters PROP_APP_ABI.split(':')
            }
        }
    }

    sourceSets.main {
        java.srcDirs "../src", "src"
        res.srcDirs "../res", 'res'
        jniLibs.srcDirs "../libs", 'libs'
        manifest.srcFile "AndroidManifest.xml"
    }

    externalNativeBuild {
        ndkBuild {
            if (!project.hasProperty("PROP_NDK_MODE") || PROP_NDK_MODE.compareTo('none') != 0) {
                // skip the NDK Build step if PROP_NDK_MODE is none
                path "jni/Android.mk"
            }
        }
    }

    signingConfigs {

        release {
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        release {
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }

            externalNativeBuild {
                ndkBuild {
                    arguments 'NDK_DEBUG=0'
                }
            }
        }

        debug {
            debuggable true
            jniDebuggable true
            renderscriptDebuggable true
            externalNativeBuild {
                ndkBuild {
                    arguments 'NDK_DEBUG=1'
                }
            }
        }
    }
}

android.applicationVariants.all { variant ->
    // delete previous files first
    delete "${buildDir}/intermediates/merged_assets/${variant.dirName}"

    variant.mergeAssets.doLast {
        def sourceDir = "${buildDir}/../../../../.."

        copy {
            from "${sourceDir}"
            include "assets/**"
            include "src/**"
            include "jsb-adapter/**"
            into outputDir
        }

        copy {
            from "${sourceDir}/main.js"
            from "${sourceDir}/project.json"
            into outputDir
        }
    }
}

dependencies {
    implementation fileTree(dir: '../libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: "../../../cocos2d-x/cocos/platform/android/java/libs", include: ['*.jar'])
    implementation project(':libcocos2dx')

    // adjust打点分析归因
    implementation 'com.adjust.sdk:adjust-android:4.36.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    implementation 'com.adjust.sdk:adjust-android-meta-referrer:5.0.0'
    implementation 'androidx.core:core:1.13.1'

    //max广告相关sdk
    implementation 'com.applovin:applovin-sdk:12.0.0'
    implementation 'com.applovin.mediation:google-adapter:23.0.0.1'
    implementation 'com.applovin.mediation:inmobi-adapter:10.6.7.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'com.applovin.mediation:facebook-adapter:6.17.0.0'
    implementation 'com.applovin.mediation:mintegral-adapter:16.6.71.0'
    implementation 'com.applovin.mediation:bytedance-adapter:5.8.1.0.1'
    implementation 'com.applovin.mediation:ironsource-adapter:7.9.0.0.0'
    implementation 'com.applovin.mediation:vungle-adapter:7.3.0.1'
    implementation 'com.applovin.mediation:unityads-adapter:4.10.0.0'
    implementation 'com.applovin.mediation:verve-adapter:2.21.1.0'
    implementation("androidx.lifecycle:lifecycle-process:2.4.0")
    // 基础模块，必选
    implementation 'androidx.fragment:fragment:1.3.6'

    //firebase 数据分析以及崩溃收集
    implementation platform('com.google.firebase:firebase-bom:32.3.1')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'io.sentry:sentry-android:1.7.27'

    //facebook打点
    implementation 'com.facebook.android:facebook-android-sdk:latest.release'

    //谷歌内购
    def billing_version = "7.0.0"
    implementation "com.android.billingclient:billing:$billing_version"
}
