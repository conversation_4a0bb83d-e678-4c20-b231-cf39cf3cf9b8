<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.cocos.demo"
    android:installLocation="auto">

    <uses-feature android:glEsVersion="0x00020000" />

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:allowBackup="true"
        android:label="Test Game"
        android:usesCleartextTraffic="true"
        android:icon="@mipmap/ic_launcher"
        tools:targetApi="m">
        <!-- Tell Cocos2dxActivity the name of our .so -->
        <meta-data android:name="android.app.lib_name"
            android:value="cocos2djs" />

        <meta-data
            android:name="applovin.sdk.key"
            android:value="38LAa0q04_ypa_dDT0zhkRyxt4cU9BFqVCiqnNTvKBJFpTChmomNUz_8IRC1oyekFreKBuGLBqCNcoLHl4VVDJ" />

        <meta-data
            android:name="applovin.sdk.verbose_logging"
            android:value="false" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id"/>

        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token"/>

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7221440554606229~8393778808" />

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/21" />
        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />
        <!-- enable profiling when starting transactions, adjust in production env -->
        <meta-data android:name="io.sentry.traces.profiling.sample-rate" android:value="1.0" />

        <service
            android:name="org.cocos2dx.javascript.customSdk.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service
            android:name="org.cocos2dx.javascript.customSdk.AlarmService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="TIMER_ACTION" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />

        <activity
            android:name="org.cocos2dx.javascript.AppActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|uiMode"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:launchMode="singleTop"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
