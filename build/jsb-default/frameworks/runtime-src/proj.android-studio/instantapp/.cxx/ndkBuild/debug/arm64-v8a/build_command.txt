                    Executable : /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build
arguments : 
NDK_PROJECT_PATH=null
APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk
NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk
APP_ABI=arm64-v8a
NDK_ALL_ABIS=arm64-v8a
NDK_DEBUG=1
APP_PLATFORM=android-21
NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj
NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib
NDK_TOOLCHAIN_VERSION=clang
NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external
-j8
NDK_DEBUG=1
APP_SHORT_COMMANDS=false
LOCAL_SHORT_COMMANDS=false
-B
-n
jvmArgs : 


                    Build command args: []
                    Version: 1