[{"level": "INFO", "message": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "rebuilding JSON /Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/.cxx/ndkBuild/debug/arm64-v8a/android_gradle_build.json due to:", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "- expected json /Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/.cxx/ndkBuild/debug/arm64-v8a/android_gradle_build.json file is not present, will remove stale json folder", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "- missing previous command file /Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/.cxx/ndkBuild/debug/arm64-v8a/build_command.txt, will remove stale json folder", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "- command changed from previous, will remove stale json folder", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "created folder '/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/.cxx/ndkBuild/debug/arm64-v8a'", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "executing ndkBuild Executable : /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk\nNDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk\nAPP_ABI=arm64-v8a\nNDK_ALL_ABIS=arm64-v8a\nNDK_DEBUG=1\nAPP_PLATFORM=android-21\nNDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj\nNDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib\nNDK_TOOLCHAIN_VERSION=clang\nNDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external\n-j8\nNDK_DEBUG=1\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "Executable : /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk\nNDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk\nAPP_ABI=arm64-v8a\nNDK_ALL_ABIS=arm64-v8a\nNDK_DEBUG=1\nAPP_PLATFORM=android-21\nNDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj\nNDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib\nNDK_TOOLCHAIN_VERSION=clang\nNDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external\n-j8\nNDK_DEBUG=1\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "parse and convert ndk-build output to build configuration JSON", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "found application make file /Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "done executing ndkBuild", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "write command file /Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/.cxx/ndkBuild/debug/arm64-v8a/build_command.txt", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}, {"level": "INFO", "message": "JSON generation completed without problems", "file": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "tag": "debug|arm64-v8a", "diagnosticCode": "UNKNOWN"}]