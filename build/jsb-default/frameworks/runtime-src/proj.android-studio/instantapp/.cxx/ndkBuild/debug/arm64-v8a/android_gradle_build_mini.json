{"buildFiles": ["/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "NDK_DEBUG=1", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"cocos2d-debug-arm64-v8a": {"artifactName": "cocos2d", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2d.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2d.a", "runtimeFiles": []}, "extension-debug-arm64-v8a": {"artifactName": "extension", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libextension.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libextension.a", "runtimeFiles": []}, "cocos2djs-debug-arm64-v8a": {"artifactName": "cocos2djs", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2djs.so"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2djs.so", "runtimeFiles": []}, "pvmp3dec-debug-arm64-v8a": {"artifactName": "pvmp3dec", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libpvmp3dec.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libpvmp3dec.a", "runtimeFiles": []}, "cpufeatures-debug-arm64-v8a": {"artifactName": "cpufeatures", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcpufeatures.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcpufeatures.a", "runtimeFiles": []}, "editorsupport-debug-arm64-v8a": {"artifactName": "editorsupport", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libeditorsupport.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libeditorsupport.a", "runtimeFiles": []}, "vorbisidec-debug-arm64-v8a": {"artifactName": "vorbisidec", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libvorbisidec.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libvorbisidec.a", "runtimeFiles": []}, "audioengine-debug-arm64-v8a": {"artifactName": "audioengine", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libaudioengine.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libaudioengine.a", "runtimeFiles": []}, "cocos2dandroid-debug-arm64-v8a": {"artifactName": "cocos2dandroid", "buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2dandroid.a"], "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2dandroid.a", "runtimeFiles": []}}}