{"buildFiles": ["/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "NDK_DEBUG=1", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"cocos2d-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2d.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "cocos2d", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2d.a"}, "extension-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libextension.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "extension", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libextension.a"}, "cocos2djs-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2djs.so"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "cocos2djs", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2djs.so"}, "pvmp3dec-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libpvmp3dec.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "pvmp3dec", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libpvmp3dec.a"}, "cpufeatures-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcpufeatures.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "cpufeatures", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcpufeatures.a"}, "editorsupport-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libeditorsupport.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "editorsupport", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libeditorsupport.a"}, "vorbisidec-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libvorbisidec.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "vorbisidec", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libvorbisidec.a"}, "audioengine-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libaudioengine.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "audioengine", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libaudioengine.a"}, "cocos2dandroid-debug-arm64-v8a": {"buildCommandComponents": ["/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/ndk-build", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Android.mk", "NDK_APPLICATION_MK=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/jni/Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj", "NDK_LIBS_OUT=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/lib", "NDK_TOOLCHAIN_VERSION=clang", "NDK_MODULE_PATH=/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/cocos:/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/cocos2d-x/external", "-j8", "NDK_DEBUG=1", "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2dandroid.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "cocos2dandroid", "output": "/Users/<USER>/Documents/Program/flat/cocos_game_frame/build/jsb-default/frameworks/runtime-src/proj.android-studio/instantapp/build/intermediates/ndkBuild/debug/obj/local/arm64-v8a/libcocos2dandroid.a"}}, "toolchains": {"toolchain-arm64-v8a": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++"}}, "cFileExtensions": ["c"], "cppFileExtensions": ["cc", "cpp"]}