package org.cocos2dx.javascript.customSdk;

import android.provider.Settings;
import android.util.Log;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustAttribution;
import com.adjust.sdk.AdjustConfig;
import com.adjust.sdk.OnAttributionChangedListener;
import com.adjust.sdk.OnDeviceIdsRead;

import org.cocos2dx.javascript.AppActivity;

public class AdjustManager {
    private static AdjustManager _instance = null;

    public static AdjustManager getInstance() {
        if (_instance == null) {
            _instance = new AdjustManager();
        }
        return _instance;
    }

    public String gaid = "";

    public void init(AppActivity activity, String adjustToken, String facebookId) {
        String environment = AdjustConfig.ENVIRONMENT_PRODUCTION;
        AdjustConfig config = new AdjustConfig(activity, adjustToken, environment);
        config.setFbAppId(facebookId);
        config.setUrlStrategy(AdjustConfig.URL_STRATEGY_INDIA);
        config.setOnAttributionChangedListener(new OnAttributionChangedListener() {
            @Override
            public void onAttributionChanged(AdjustAttribution adjustAttribution) {
                Log.d("adjust", adjustAttribution.toString());
            }
        });
        Adjust.onCreate(config);

        gaid = Settings.System.getString(activity.getContentResolver(), Settings.Secure.ANDROID_ID);
        SDKUtils.googledAdId = gaid;
        Adjust.getGoogleAdId(activity, new OnDeviceIdsRead() {
            @Override
            public void onGoogleAdIdRead(String googleAdId) {
                gaid = googleAdId;
                SDKUtils.googledAdId = googleAdId;
            }
        });
    }


}
