// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    repositories {
        google()
        jcenter()
        maven {
            url "repo"
        }
        mavenCentral()
        maven { url "https://android-sdk.is.com" }
        // mintegral
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        // pangle
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://jitpack.io" }
        // verve
        maven { url "https://verve.jfrog.io/artifactory/verve-gradle-release" }
        // smaato
        maven { url "https://s3.amazonaws.com/smaato-sdk-releases/" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'com.google.gms:google-services:4.4.1'


        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        flatDir {
            dirs 'libs'
        }

        maven {
            url "repo"
        }
        mavenCentral()
        maven { url "https://android-sdk.is.com" }
        // mintegral
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        // pangle
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://jitpack.io" }
        // verve
        maven { url "https://verve.jfrog.io/artifactory/verve-gradle-release" }
        // smaato
        maven { url "https://s3.amazonaws.com/smaato-sdk-releases/" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
