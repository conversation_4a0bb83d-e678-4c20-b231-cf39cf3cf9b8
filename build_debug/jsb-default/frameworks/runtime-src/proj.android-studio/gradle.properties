# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

PROP_APP_ABI=armeabi-v7a:arm64-v8a
PROP_BUILD_TOOLS_VERSION=28.0.3
PROP_COMPILE_SDK_VERSION=34
PROP_MIN_SDK_VERSION=21
PROP_TARGET_SDK_VERSION=34
RELEASE_KEY_ALIAS=debug_keystore
RELEASE_KEY_PASSWORD=123456
RELEASE_STORE_FILE=/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/static/build-templates/native/debug.keystore
RELEASE_STORE_PASSWORD=123456
android.enableJetifier=true
android.injected.testOnly=false
android.useAndroidX=true
android.useDeprecatedNdk=true
org.gradle.jvmargs=-Xmx2048M -Dkotlin.daemon.jvm.options\="-Xmx2048M"
# android.enableJetifier=true